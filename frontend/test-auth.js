// Simple test to verify authentication and routing
console.log('Testing authentication and routing...');

async function testAuthentication() {
  try {
    // Test 1: Check if the login form is accessible
    console.log('🔍 Testing login page access...');
    const loginResponse = await fetch('http://localhost:5175/');
    const loginHtml = await loginResponse.text();

    console.log('✅ Frontend is accessible');
    console.log('📄 Response contains login elements:',
      loginHtml.includes('login') ||
      loginHtml.includes('Login') ||
      loginHtml.includes('username') ||
      loginHtml.includes('password')
    );

    // Test 2: Check if we can access protected routes (should redirect to login)
    console.log('🔍 Testing protected route access...');
    const protectedResponse = await fetch('http://localhost:5175/admin/dashboard');
    const protectedHtml = await protectedResponse.text();

    console.log('✅ Protected route test completed');
    console.log('📄 Protected route shows login:',
      protectedHtml.includes('login') ||
      protectedHtml.includes('Login') ||
      protectedHtml.includes('username') ||
      protectedHtml.includes('password')
    );

    // Test 3: Check what the app is actually rendering
    console.log('🔍 Checking app content...');
    console.log('📄 HTML length:', loginHtml.length);
    console.log('📄 Contains React root:', loginHtml.includes('root'));
    console.log('📄 Contains script tags:', loginHtml.includes('<script'));
    console.log('📄 Contains CSS:', loginHtml.includes('<style') || loginHtml.includes('.css'));

    // Show a snippet of the HTML to debug
    const snippet = loginHtml.substring(0, 500);
    console.log('📄 HTML snippet:', snippet);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAuthentication().then(() => {
  console.log('🎯 Authentication tests completed');
});
