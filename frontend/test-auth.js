// Simple test to verify authentication and routing
console.log('Testing authentication and routing...');

async function testAuthentication() {
  try {
    // Test 1: Test backend login API directly
    console.log('🔍 Testing backend login API...');
    const loginResponse = await fetch('http://localhost:8000/api/auth/login/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    let loginData = null;
    if (loginResponse.ok) {
      loginData = await loginResponse.json();
      console.log('✅ Backend login successful');
      console.log('📄 User role:', loginData.user.role.id);
      console.log('📄 Has access token:', !!loginData.access_token);
      console.log('📄 Has user data:', !!loginData.user);
    } else {
      console.log('❌ Backend login failed:', loginResponse.status);
    }

    // Test 2: Test frontend accessibility
    console.log('🔍 Testing frontend access...');
    const frontendResponse = await fetch('http://localhost:5175/');
    console.log('✅ Frontend accessible:', frontendResponse.ok);

    // Test 3: Test if we can verify token
    if (loginData?.access_token) {
      console.log('🔍 Testing token verification...');
      const verifyResponse = await fetch('http://localhost:8000/api/auth/user/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${loginData.access_token}`
        }
      });

      if (verifyResponse.ok) {
        const userData = await verifyResponse.json();
        console.log('✅ Token verification successful');
        console.log('📄 Verified user:', userData.username);
      } else {
        console.log('❌ Token verification failed:', verifyResponse.status);
      }
    } else {
      console.log('⚠️ No access token to test verification');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAuthentication().then(() => {
  console.log('🎯 Authentication tests completed');
});
