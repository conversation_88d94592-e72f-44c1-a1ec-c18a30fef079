/**
 * Test script to verify KPI target values fix
 * Run this in the browser console after logging in and navigating to KPI pages
 */

// Test the formatKPIValue function with string inputs
function testFormatKPIValueFix() {
  console.log('🧪 Testing formatKPIValue with string inputs...')
  
  // Import the function (assuming it's available globally or through modules)
  // This would work if the function is imported in the page
  
  const testCases = [
    { value: "85.00", unit: "%", expected: "85.0%" },
    { value: "100000.00", unit: "SAR", expected: "formatted currency" },
    { value: 85, unit: "%", expected: "85.0%" },
    { value: null, unit: "%", expected: "--" },
    { value: undefined, unit: "%", expected: "--" },
    { value: "0.00", unit: "%", expected: "0.0%" },
    { value: "", unit: "%", expected: "--" },
    { value: "invalid", unit: "%", expected: "--" }
  ]
  
  console.log('Test cases to verify:')
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. Input: ${JSON.stringify(testCase.value)} (${typeof testCase.value}) -> Expected: ${testCase.expected}`)
  })
}

// Test API response and target value display
async function testKPITargetValuesDisplay() {
  try {
    console.log('🎯 Testing KPI Target Values Display...')
    
    // Get the auth token
    const token = localStorage.getItem('token')
    if (!token) {
      console.error('❌ No auth token found. Please log in first.')
      return
    }
    
    // Fetch KPI data directly from API
    const response = await fetch('http://localhost:8000/api/kpi-metrics/', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      console.error('❌ API request failed:', response.status)
      return
    }
    
    const data = await response.json()
    console.log('📊 Raw API Response:', data)
    
    // Check the first few KPIs
    const kpis = data.results || data
    if (Array.isArray(kpis)) {
      console.log(`📈 Found ${kpis.length} KPIs`)
      
      kpis.slice(0, 5).forEach((kpi, index) => {
        console.log(`🎯 KPI ${index + 1}:`, {
          id: kpi.id,
          name: kpi.name,
          target_value: kpi.target_value,
          target_type: typeof kpi.target_value,
          current_value: kpi.current_value,
          unit: kpi.unit,
          status: kpi.status
        })
        
        // Test the condition logic
        const hasValidTarget = kpi.target_value !== null && 
                              kpi.target_value !== undefined && 
                              kpi.target_value !== 0 && 
                              kpi.target_value !== '0' && 
                              kpi.target_value !== '0.00'
        
        console.log(`✅ Target validation for ${kpi.name}:`, {
          original: kpi.target_value,
          hasValidTarget: hasValidTarget,
          shouldShow: hasValidTarget ? 'YES' : 'NO (will show --)'
        })
      })
      
      // Check if any KPIs have string target values
      const stringTargets = kpis.filter(kpi => typeof kpi.target_value === 'string')
      console.log(`🔍 KPIs with string target values: ${stringTargets.length}`)
      
      if (stringTargets.length > 0) {
        console.log('String target examples:', stringTargets.slice(0, 3).map(kpi => ({
          name: kpi.name,
          target_value: kpi.target_value,
          type: typeof kpi.target_value
        })))
      }
      
    } else {
      console.error('❌ Unexpected data format:', typeof kpis)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Test the DOM elements showing target values
function testTargetValueDOM() {
  console.log('🔍 Checking DOM for target value displays...')
  
  // Look for elements that might contain target values
  const targetElements = document.querySelectorAll('[class*="target"], [class*="Target"]')
  console.log(`Found ${targetElements.length} potential target elements`)
  
  targetElements.forEach((element, index) => {
    console.log(`Target element ${index + 1}:`, {
      text: element.textContent?.trim(),
      classes: element.className,
      hasDoubleDash: element.textContent?.includes('--')
    })
  })
  
  // Look for elements containing "--"
  const dashElements = Array.from(document.querySelectorAll('*')).filter(el => 
    el.textContent?.trim() === '--' && el.children.length === 0
  )
  
  console.log(`Found ${dashElements.length} elements showing "--"`)
  dashElements.slice(0, 5).forEach((element, index) => {
    console.log(`Dash element ${index + 1}:`, {
      text: element.textContent,
      classes: element.className,
      parent: element.parentElement?.className
    })
  })
}

// Main test function
function runTargetValueTests() {
  console.log('🚀 Starting KPI Target Value Tests...')
  console.log('=' .repeat(50))
  
  testFormatKPIValueFix()
  console.log('=' .repeat(50))
  
  testKPITargetValuesDisplay()
  console.log('=' .repeat(50))
  
  setTimeout(() => {
    testTargetValueDOM()
    console.log('=' .repeat(50))
    console.log('✅ Tests completed! Check the results above.')
  }, 2000)
}

// Instructions
console.log(`
🧪 KPI Target Values Fix Test Script Loaded!

To run the tests:
1. Make sure you're logged in to the EMS application
2. Navigate to a KPI page (e.g., /analytics/kpi-management or /analytics/modern-kpi)
3. Run: runTargetValueTests()

Or run individual tests:
- testFormatKPIValueFix() - Test the formatting function
- testKPITargetValuesDisplay() - Test API response and validation logic
- testTargetValueDOM() - Check DOM elements for target displays
`)

// Auto-run if we're on a KPI page
if (window.location.pathname.includes('kpi') || window.location.pathname.includes('analytics')) {
  console.log('🎯 Detected KPI page, running tests automatically in 3 seconds...')
  setTimeout(runTargetValueTests, 3000)
}
