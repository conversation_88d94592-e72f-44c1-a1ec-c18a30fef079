// Service Worker Fix - Minimal Implementation
// This fixes the "Failed to convert value to 'Response'" error by being very conservative

self.addEventListener('fetch', function(event) {
  // Skip ALL API calls to avoid conflicts
  if (event.request.url.includes('/api/')) {
    return;
  }

  // Skip localhost requests to avoid conflicts
  if (event.request.url.includes('localhost:8000')) {
    return;
  }

  // Skip PDF-related requests
  if (event.request.url.includes('pdf')) {
    return;
  }

  // Skip export-related requests
  if (event.request.url.includes('export')) {
    return;
  }

  // Only handle static assets (CSS, JS, images)
  if (event.request.url.includes('.css') ||
      event.request.url.includes('.js') ||
      event.request.url.includes('.png') ||
      event.request.url.includes('.jpg') ||
      event.request.url.includes('.svg')) {

    event.respondWith(
      fetch(event.request)
        .then(function(response) {
          // Only cache successful responses
          if (response && response.status === 200) {
            return response;
          }
          return response;
        })
        .catch(function(error) {
          console.log('Service worker fetch failed for static asset:', error);
          // Don't try to provide fallback for static assets
          return fetch(event.request);
        })
    );
  }
});

self.addEventListener('install', function(event) {
  console.log('Service worker installing (minimal mode)...');
  self.skipWaiting();
});

self.addEventListener('activate', function(event) {
  console.log('Service worker activating (minimal mode)...');
  event.waitUntil(self.clients.claim());
});
