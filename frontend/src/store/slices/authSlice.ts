import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { authService, User, LoginCredentials, handleApiError } from '../../services'



export interface UserPreferences {
  language: 'ar' | 'en'
  theme: 'light' | 'dark'
  dashboardLayout: string[]
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
}

interface AuthState {
  user: User | null
  // SECURITY FIX: Removed token from state - tokens are in httpOnly cookies only
  isAuthenticated: boolean
  isLoading: boolean
  isVerifying: boolean // FIXED: Separate loading state for token verification
  error: string | null
  loginAttempts: number
  lastLoginAttempt: number | null
  // CSRF token management
  csrfToken: string | null
  csrfTokenExpiry: number
  isFetchingCsrf: boolean
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  isVerifying: false,
  error: null,
  loginAttempts: 0,
  lastLoginAttempt: null,
  // CSRF token management
  csrfToken: null,
  csrfTokenExpiry: 0,
  isFetchingCsrf: false,
}

// Async thunks using real API service
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      console.log('🔄 AuthSlice: Starting login process...')
      const authResponse = await authService.login(credentials)
      console.log('🔄 AuthSlice: Login response received:', {
        hasUser: !!authResponse.user,
        hasToken: !!authResponse.access_token,
        username: authResponse.user?.username
      })

      return {
        user: authResponse.user
      }
    } catch (error) {
      console.error('🔄 AuthSlice: Login failed:', error)
      return rejectWithValue(handleApiError(error))
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout()
      return null
    } catch (error) {
      // Even if logout API fails, we should clear local state
      console.warn('Logout API failed:', error)
      return null
    }
  }
)

export const verifyToken = createAsyncThunk(
  'auth/verifyToken',
  async (_, { rejectWithValue }) => {
    try {
      const user = await authService.verifyToken()
      return user
    } catch (error) {
      return rejectWithValue(handleApiError(error))
    }
  }
)

// TEMPORARILY DISABLE CSRF TOKEN FETCHING TO FIX INFINITE LOOP
// export const fetchCSRFToken = createAsyncThunk(
//   'auth/fetchCSRFToken',
//   async (_, { getState, rejectWithValue }) => {
//     try {
//       const state = getState() as { auth: AuthState }
//
//       // Don't fetch if already fetching or token is still valid
//       if (state.auth.isFetchingCsrf) {
//         return null
//       }
//
//       // Check if current token is still valid (5 minutes)
//       const now = Date.now()
//       if (state.auth.csrfToken && now < state.auth.csrfTokenExpiry) {
//         return state.auth.csrfToken
//       }
//
//       // Fetch new token
//       const response = await fetch('/api/auth/csrf/', {
//         method: 'GET',
//         credentials: 'include',
//         headers: {
//           'Accept': 'application/json',
//         }
//       })
//
//       if (response.ok) {
//         const data = await response.json()
//         return data.csrfToken || null
//       }
//
//       throw new Error('Failed to fetch CSRF token')
//     } catch (error) {
//       return rejectWithValue(handleApiError(error))
//     }
//   }
// )

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData: Partial<User>, { rejectWithValue }) => {
    try {
      const updatedUser = await authService.updateProfile(profileData)
      return updatedUser
    } catch (error) {
      return rejectWithValue(handleApiError(error))
    }
  }
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      if (state.user && state.user.profile) {
        // Update the user profile with preference-like data
        state.user.profile = {
          ...state.user.profile,
          preferred_language: action.payload.language || state.user.profile.preferred_language
        }
      }
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0
      state.lastLoginAttempt = null
    },
    setAuthenticated: (state, action: PayloadAction<boolean>) => {
      state.isAuthenticated = action.payload
    },
    clearInconsistentState: (state) => {
      // Clear state when authentication is inconsistent
      state.user = null
      state.isAuthenticated = false
      state.isLoading = false
    },
    clearCSRFToken: (state) => {
      state.csrfToken = null
      state.csrfTokenExpiry = 0
      state.isFetchingCsrf = false
      state.error = null
    }
    // TEMPORARILY DISABLE CSRF TOKEN REDUCER TO FIX INFINITE LOOP
    // clearCSRFToken: (state) => {
    //   state.csrfToken = null
    //   state.csrfTokenExpiry = 0
    //   state.isFetchingCsrf = false
    // }
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true
        // Don't clear error immediately - let user see previous error until new result
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        console.log('🔄 AuthSlice: Login fulfilled, updating state...')

        state.isLoading = false
        state.error = null
        state.user = action.payload.user
        state.isAuthenticated = true
        state.loginAttempts = 0
        state.lastLoginAttempt = null

        console.log('🔄 AuthSlice: Auth state updated after login:', {
          hasUser: !!state.user,
          isAuthenticated: state.isAuthenticated,
          username: state.user?.username
        })
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.loginAttempts += 1
        state.lastLoginAttempt = Date.now()
      })
      // Logout
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null
        // SECURITY FIX: No token in state - cleared by httpOnly cookie removal
        state.isAuthenticated = false
        state.error = null
        state.isLoading = false
        // Clear CSRF token on logout
        state.csrfToken = null
        state.csrfTokenExpiry = 0
        state.isFetchingCsrf = false
      })
      .addCase(logoutUser.rejected, (state) => {
        // Even if logout fails, clear the state
        state.user = null
        // SECURITY FIX: No token in state - cleared by httpOnly cookie removal
        state.isAuthenticated = false
        state.isLoading = false
      })
      // Verify token
      .addCase(verifyToken.pending, (state) => {
        state.isVerifying = true // FIXED: Use separate verification loading state
      })
      .addCase(verifyToken.fulfilled, (state, action) => {
        state.isVerifying = false
        state.user = action.payload
        state.isAuthenticated = true
      })
      .addCase(verifyToken.rejected, (state) => {
        state.isVerifying = false
        state.user = null
        state.isAuthenticated = false
      })
      // Update profile
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // CSRF token management
      .addCase(fetchCSRFToken.pending, (state) => {
        state.isFetchingCsrf = true
      })
      .addCase(fetchCSRFToken.fulfilled, (state, action) => {
        state.isFetchingCsrf = false
        if (action.payload) {
          state.csrfToken = action.payload
          state.csrfTokenExpiry = Date.now() + (5 * 60 * 1000) // 5 minutes
        }
      })
      .addCase(fetchCSRFToken.rejected, (state) => {
        state.isFetchingCsrf = false
      })
  },
})

// CSRF token async thunk
export const fetchCSRFToken = createAsyncThunk(
  'auth/fetchCSRFToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/auth/csrf/', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
        }
      })

      if (response.ok) {
        const data = await response.json()
        return data.csrfToken || null
      }

      return null
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error)
      return rejectWithValue('Failed to fetch CSRF token')
    }
  }
)

export const { clearError, updateUserPreferences, resetLoginAttempts, setAuthenticated, clearInconsistentState, clearCSRFToken } = authSlice.actions



export default authSlice.reducer
