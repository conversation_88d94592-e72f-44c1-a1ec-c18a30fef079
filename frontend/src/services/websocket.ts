/**
 * Enterprise WebSocket Service for Real-time KPI Updates
 *
 * This service provides real-time communication for:
 * - KPI value updates
 * - System notifications
 * - Dashboard streaming
 * - Enterprise monitoring
 *
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Authentication token management
 * - Message queuing during disconnection
 * - Performance monitoring
 * - Enterprise-grade error handling
 */

export interface WebSocketMessage {
  type: string
  data?: unknown
  timestamp?: string
}

export interface KPIUpdateMessage {
  type: 'kpi_update'
  kpi_id: string
  value: number
  timestamp: string
  calculation_method: string
}

export interface KPIAlertMessage {
  type: 'kpi_alert'
  kpi_id: string
  alert_type: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
}

export interface NotificationData {
  id: string
  type: string
  title: string
  message: string
  timestamp: number
  read?: boolean
}

export interface ConnectionStatus {
  connected: boolean
  enabled: boolean
  reconnectAttempts: number
  lastError: string | null
  lastConnected?: Date
  lastDisconnected?: Date
}

class EnhancedWebSocketService {
  private ws: WebSocket | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private maxReconnectDelay = 30000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private messageQueue: WebSocketMessage[] = []
  private listeners: Map<string, ((data: unknown) => void)[]> = new Map()
  private connectionStatus: ConnectionStatus = {
    connected: false,
    enabled: true,
    reconnectAttempts: 0,
    lastError: null
  }

  constructor() {
    // FIXED: Use import.meta.env instead of process.env for Vite
    if (import.meta.env.DEV) {
      console.log('🚀 Enhanced WebSocket service initialized')
    }
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus }
  }

  /**
   * Connect to WebSocket server with authentication
   */
  connect(endpoint: string = 'kpi'): void {
    // TEMPORARY FIX: Disable WebSocket connections until backend WebSocket server is properly configured
    console.log('🚫 WebSocket connection disabled - backend WebSocket server not available')
    this.connectionStatus.lastError = 'WebSocket server not configured'
    return

    if (this.ws && this.isConnected) {
      console.log('⚠️ WebSocket already connected')
      return
    }

    try {
      // SECURITY FIX: Use httpOnly cookies instead of localStorage tokens
      // Check if user is authenticated by checking for auth cookies
      const hasAuthCookie = document.cookie.includes('access_token') || document.cookie.includes('sessionid')
      if (!hasAuthCookie) {
        console.warn('⚠️ No authentication cookie found, WebSocket connection may fail')
        // Don't return here - let the WebSocket attempt connection and handle auth on server side
      }

      const wsUrl = this.buildWebSocketUrl(endpoint)
      console.log(`🔌 Connecting to WebSocket: ${wsUrl}`)

      this.ws = new WebSocket(wsUrl)
      this.setupEventHandlers()

    } catch (error) {
      console.error('❌ WebSocket connection error:', error)
      this.connectionStatus.lastError = error instanceof Error ? error.message : 'Connection failed'
      this.handleReconnect()
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }

    this.isConnected = false
    this.connectionStatus.connected = false
    this.connectionStatus.lastDisconnected = new Date()

    console.log('🔌 WebSocket disconnected')
  }

  /**
   * Send message to WebSocket server
   */
  send(message: WebSocketMessage): void {
    if (this.isConnected && this.ws) {
      try {
        this.ws.send(JSON.stringify(message))
        console.log('📤 WebSocket message sent:', message.type)
      } catch (error) {
        console.error('❌ Failed to send WebSocket message:', error)
        this.messageQueue.push(message)
      }
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(message)
      console.log('📥 Message queued (not connected):', message.type)
    }
  }

  /**
   * Add event listener
   */
  on(event: string, callback: (data: unknown) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: (data: unknown) => void): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * Subscribe to specific KPI updates
   */
  subscribeToKPI(kpiId: string): void {
    this.send({
      type: 'subscribe_kpi',
      data: { kpi_id: kpiId }
    })
  }

  /**
   * Enable WebSocket connection
   */
  enableConnection(): void {
    this.connectionStatus.enabled = true
    if (!this.isConnected) {
      this.connect()
    }
  }

  /**
   * Disable WebSocket connection
   */
  disableConnection(): void {
    this.connectionStatus.enabled = false
    this.disconnect()
  }

  // Private methods

  private buildWebSocketUrl(endpoint: string): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    // FIXED: Use import.meta.env and correct port mapping
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws'
    const host = wsUrl.replace('ws://', '').replace('wss://', '').replace('/ws', '')
    return `${protocol}//${host}/ws/${endpoint}/`
  }

  private setupEventHandlers(): void {
    if (!this.ws) return

    this.ws.onopen = () => {
      console.log('✅ WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.connectionStatus.connected = true
      this.connectionStatus.reconnectAttempts = 0
      this.connectionStatus.lastConnected = new Date()
      this.connectionStatus.lastError = null

      // Start heartbeat
      this.startHeartbeat()

      // Send queued messages
      this.processMessageQueue()

      // Emit connection event
      this.emit('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        console.log('📨 WebSocket message received:', message.type)

        // Handle different message types
        this.handleMessage(message)

      } catch (error) {
        console.error('❌ Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log(`🔌 WebSocket closed: ${event.code} - ${event.reason}`)
      this.isConnected = false
      this.connectionStatus.connected = false
      this.connectionStatus.lastDisconnected = new Date()

      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
      }

      // Attempt reconnection if enabled
      if (this.connectionStatus.enabled && event.code !== 1000) {
        this.handleReconnect()
      }

      this.emit('disconnected', { code: event.code, reason: event.reason })
    }

    this.ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error)
      this.connectionStatus.lastError = 'Connection error'
      this.emit('error', error)
    }
  }

  private handleMessage(message: Record<string, unknown>): void {
    const messageType = message.type as string

    switch (messageType) {
      case 'kpi_update':
        this.emit('kpi_update', message as unknown as KPIUpdateMessage)
        break
      case 'kpi_alert':
        this.emit('kpi_alert', message as unknown as KPIAlertMessage)
        break
      case 'notification':
        this.emit('notification', message)
        break
      case 'pong':
        // Heartbeat response
        break
      case 'connection_established':
        console.log('🎉 WebSocket connection established')
        break
      default:
        if (messageType) {
          this.emit(messageType, message)
        }
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached')
      this.connectionStatus.lastError = 'Max reconnection attempts reached'
      this.emit('connection_failed')
      return
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.maxReconnectDelay
    )

    this.reconnectAttempts++
    this.connectionStatus.reconnectAttempts = this.reconnectAttempts

    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      if (this.connectionStatus.enabled) {
        this.connect()
      }
    }, delay)
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' })
      }
    }, 30000) // 30 seconds
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message)
      }
    }
  }

  private emit(event: string, data?: unknown): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`❌ Error in WebSocket event listener for ${event}:`, error)
        }
      })
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return this.isConnected
  }
}

// Export singleton instance
export const enhancedWebSocketService = new EnhancedWebSocketService()
export default enhancedWebSocketService

// Legacy export for backward compatibility
export const webSocketService = enhancedWebSocketService