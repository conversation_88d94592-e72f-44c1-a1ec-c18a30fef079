/**
 * API Service Layer for EMS Application
 * Handles all HTTP requests to the Django backend
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// API Response Types
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  status: number
}

export interface ApiErrorInterface {
  message: string
  status: number
  details?: Record<string, unknown>
}

// Authentication Types
export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  access_token: string
  refresh_token: string
  token_type: string
  user: User
  message: string
}

export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  role: UserRole
  profile: UserProfile
}

export interface UserRole {
  id: string
  name: string
  nameAr: string
  permissions: Permission[]
  dashboardConfig: DashboardConfig
}

export interface Permission {
  module: string
  actions: string[]
}

export interface DashboardConfig {
  allowedRoutes: string[]
  defaultWidgets: string[]
  customizations: Record<string, unknown>
}

export interface UserProfile {
  avatar?: string
  phone?: string
  department?: string
  position?: string
  preferred_language: 'ar' | 'en'
  timezone: string
}

// HTTP Client Class
class ApiClient {
  private baseURL: string
  private refreshPromise: Promise<void> | null = null // FIXED: Prevent race conditions

  constructor(baseURL: string) {
    this.baseURL = baseURL

    // SECURITY FIX: Set up centralized auth error handling
    this.setupAuthErrorHandling()

    if (process.env.NODE_ENV === 'development') {
      console.log('ApiClient initialized - authentication via httpOnly cookies')
    }
  }

  // SECURITY FIX: Centralized authentication error handling
  private setupAuthErrorHandling() {
    // Listen for auth errors from various parts of the app
    window.addEventListener('auth:error', (event: any) => {
      const { status, message } = event.detail
      if (status === 401) {
        console.warn('Authentication error detected, attempting token refresh...')
        // Try to refresh token automatically
        this.refreshTokenSafe().catch(() => {
          // If refresh fails, emit logout event
          window.dispatchEvent(new CustomEvent('auth:logout', {
            detail: 'Token refresh failed'
          }))
        })
      }
    })

    // Listen for logout events
    window.addEventListener('auth:logout', () => {
      console.log('Logout event received, redirecting to login...')
      // Redirect to login (cookies cleared by backend)
      window.location.href = '/login'
    })
  }



  // Get authentication headers with JWT token
  private async getHeaders(): Promise<HeadersInit> {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }

    // Add JWT token if available
    const token = localStorage.getItem('access_token')
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    return headers
  }

  // FIXED: Generic request method with improved error handling and retry logic
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`

    // PERFORMANCE FIX: Check request throttling to prevent excessive API calls
    // TEMPORARILY DISABLED for debugging timeout issues
    // const { requestThrottler } = await import('../utils/requestThrottling')
    // if (!requestThrottler.shouldAllowRequest(url)) {
    //   throw new Error(`Request throttled: ${endpoint}`)
    // }
    const maxRetries = 3
    const retryDelay = Math.pow(2, retryCount) * 1000 // Exponential backoff

    // SECURITY FIX: Get headers asynchronously to include CSRF token
    const baseHeaders = await this.getHeaders()

    const config: RequestInit = {
      ...options,
      headers: {
        ...baseHeaders,
        ...options.headers,
      },
      // SECURITY FIX: Include credentials for httpOnly cookies
      credentials: 'include',
      // FIXED: Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(30000), // 30 second timeout
    }

    try {
      console.log('🌐 Making API request:', { url, method: config.method, headers: config.headers })
      const startTime = Date.now()
      const response = await fetch(url, config)
      const endTime = Date.now()
      console.log(`🌐 API response received in ${endTime - startTime}ms:`, {
        url,
        status: response.status,
        statusText: response.statusText
      })

      // CRITICAL FIX: Use TokenRefreshManager to prevent race conditions
      if (response.status === 401 && retryCount === 0) {
        try {
          const { tokenRefreshManager } = await import('../utils/tokenRefreshManager')
          const refreshSuccess = await tokenRefreshManager.refreshToken()

          if (refreshSuccess) {
            // Retry the original request
            return this.request(endpoint, options, retryCount + 1)
          } else {
            throw new Error('Token refresh failed')
          }
        } catch (refreshError) {
          // TokenRefreshManager handles logout and redirect
          throw new ApiError({
            message: 'Authentication failed',
            status: 401,
            details: { originalError: refreshError }
          })
        }
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // FIXED: Retry on server errors (5xx) but not client errors (4xx)
        if (response.status >= 500 && retryCount < maxRetries) {
          console.warn(`API request failed with ${response.status}, retrying in ${retryDelay}ms...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          return this.request(endpoint, options, retryCount + 1)
        }

        throw new ApiError({
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        })
      }

      const data = await response.json()

      return {
        data,
        status: response.status,
        message: data.message,
      }
    } catch (error) {
      // FIXED: Handle network errors with retry
      if (error instanceof ApiError) {
        throw error
      }

      // Retry on network errors
      if (retryCount < maxRetries && (error as Error).name !== 'AbortError') {
        console.warn(`Network error, retrying in ${retryDelay}ms...`, error)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        return this.request(endpoint, options, retryCount + 1)
      }

      // ERROR FIX: Enhanced error handling with comprehensive error handler
      const apiError = new ApiError({
        message: error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
        details: error,
      })

      // Use comprehensive error handler for better error processing
      try {
        const { comprehensiveErrorHandler } = await import('../utils/comprehensiveErrorHandler')
        comprehensiveErrorHandler.handleError(apiError, {
          component: 'ApiClient',
          action: 'request',
          url: endpoint,
          additionalData: { retryCount, options }
        }, {
          showToast: false, // Let the calling component handle UI feedback
          logToConsole: true,
          sendToService: true
        })
      } catch (importError) {
        console.warn('Failed to import comprehensive error handler:', importError)
      }

      throw apiError
    }
  }

  // FIXED: Race-condition safe token refresh method
  private async refreshTokenSafe(): Promise<void> {
    // If refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise
    }

    // Start new refresh process
    this.refreshPromise = this.performTokenRefresh()

    try {
      await this.refreshPromise
    } finally {
      // Clear the promise when done (success or failure)
      this.refreshPromise = null
    }
  }

  private async performTokenRefresh(): Promise<void> {
    // SECURITY FIX: Token refresh now handled by backend via httpOnly cookies
    // The backend will automatically refresh tokens and set new cookies
    const response = await fetch(`${this.baseURL}/auth/refresh/`, {
      method: 'POST',
      credentials: 'include', // Include httpOnly cookies
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`)
    }

    // Backend sets new httpOnly cookies automatically
    // No need to handle tokens in frontend
    if (process.env.NODE_ENV === 'development') {
      console.log('Token refreshed successfully via httpOnly cookies')
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, config?: { params?: Record<string, string | number | boolean | undefined> }): Promise<ApiResponse<T>> {
    // Handle query parameters from config
    let url = endpoint
    if (config?.params) {
      const searchParams = new URLSearchParams()
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }
    return this.request<T>(url, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // File upload method (SECURITY FIX: httpOnly cookie authentication)
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, String(additionalData[key]))
      })
    }

    // SECURITY FIX: No manual Authorization header needed
    // Authentication handled via httpOnly cookies automatically
    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData - browser sets it with boundary
      },
    })
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// Custom error class
class ApiError extends Error {
  status: number
  details?: Record<string, unknown>

  constructor({ message, status, details }: { message: string; status: number; details?: Record<string, unknown> }) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.details = details
  }
}

export { ApiError }

// REMOVED: Duplicate authAPI - use AuthService class instead
// All authentication functionality is now consolidated in AuthService

// Dashboard API
export interface DashboardStats {
  total_employees: number
  total_departments: number
  active_projects: number
  pending_tasks: number
  pending_leave_requests: number
  monthly_expenses: number
  system_health: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
  }
}

export const dashboardAPI = {
  getStats: async (): Promise<DashboardStats> => {
    // FIXED: Remove circular import - use simple caching instead
    const cacheKey = 'dashboard-stats'
    const cacheTimeout = 10000 // 10 seconds

    // Simple in-memory cache to avoid circular imports
    const now = Date.now()
    const cached = (globalThis as any).__dashboardCache

    if (cached && cached.key === cacheKey && (now - cached.timestamp) < cacheTimeout) {
      return cached.data
    }

    console.log('📊 Fetching dashboard stats from API')
    const response = await apiClient.get<DashboardStats>('/dashboard-stats/')

    // Cache the result
    ;(globalThis as any).__dashboardCache = {
      key: cacheKey,
      data: response.data,
      timestamp: now
    }

    return response.data
  },
}

// Utility functions
export const isApiError = (error: unknown): error is ApiError => {
  return error instanceof ApiError
}

export const handleApiError = (error: unknown): string => {
  if (isApiError(error)) {
    return error.message
  }

  if (error instanceof Error) {
    return error.message
  }

  return 'An unexpected error occurred'
}
