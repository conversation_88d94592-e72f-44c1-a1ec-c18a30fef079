/**
 * Authentication Service
 * Handles all authentication-related API calls
 */

import { apiClient, ApiResponse, LoginCredentials, AuthResponse, User } from './api'

export class AuthService {
  /**
   * Login user with credentials - SIMPLIFIED VERSION
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('AuthService: Sending login request to API...')

      // SIMPLIFIED: Direct fetch call to avoid complex API client issues
      const response = await fetch('http://localhost:8000/api/auth/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Login failed' }))
        throw new Error(errorData.message || `HTTP ${response.status}`)
      }

      const data = await response.json()
      console.log('AuthService: Login API response received:', {
        status: response.status,
        hasAccessToken: !!data.access,
        hasUser: !!data.user,
      })

      // SIMPLIFIED: Store token in localStorage for now (we can improve security later)
      if (data.access) {
        localStorage.setItem('access_token', data.access)
        localStorage.setItem('refresh_token', data.refresh || '')
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Login successful - tokens stored')
      }

      // Return in expected format
      return {
        access_token: data.access,
        refresh_token: data.refresh,
        token_type: 'Bearer',
        user: data.user,
        message: 'Login successful'
      }
    } catch (error) {
      console.error('AuthService: Login failed:', error)
      throw error
    }
  }

  /**
   * Logout user and clear authentication state - SIMPLIFIED VERSION
   */
  async logout(): Promise<void> {
    try {
      // SIMPLIFIED: Clear localStorage tokens
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')

      // Try to call logout endpoint but don't fail if it doesn't work
      try {
        await fetch('http://localhost:8000/api/auth/logout/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
          },
        })
      } catch (logoutError) {
        console.warn('Logout API call failed, but local tokens cleared:', logoutError)
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Logout successful - tokens cleared')
      }
    } catch (error) {
      console.warn('AuthService: Logout failed:', error)
    }
  }

  /**
   * Verify current token and get user data - SIMPLIFIED VERSION
   */
  async verifyToken(): Promise<User> {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        throw new Error('No token found')
      }

      const response = await fetch('http://localhost:8000/api/auth/user/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const userData = await response.json()
      return userData
    } catch (error) {
      console.warn('Token verification failed:', error)
      // Clear invalid token
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      throw error
    }
  }

  /**
   * Register new user (if registration is enabled)
   */
  async register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
  }): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/register/', userData)

      // SECURITY FIX: No token storage needed - backend sets httpOnly cookies
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Registration successful - authentication cookies set by backend')
      }

      return response.data
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset/', { email })
    } catch (error) {
      console.error('Password reset request failed:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset-confirm/', {
        token,
        password: newPassword
      })
    } catch (error) {
      console.error('Password reset failed:', error)
      throw error
    }
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/change-password/', {
        current_password: currentPassword,
        new_password: newPassword
      })
    } catch (error) {
      console.error('Password change failed:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.patch<User>('/auth/profile/', profileData)
      return response.data
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    }
  }


}

// Create and export auth service instance
export const authService = new AuthService()

// Export for convenience
export default authService
