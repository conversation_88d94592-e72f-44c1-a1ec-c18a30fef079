/**
 * Authentication Service
 * Handles all authentication-related API calls
 */

import { apiClient, ApiResponse, LoginCredentials, AuthResponse, User } from './api'

export class AuthService {
  /**
   * Login user with credentials
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('AuthService: Sending login request to API...')
      const response = await apiClient.post<AuthResponse>('/auth/login/', credentials)
      console.log('AuthService: Login API response received:', {
        status: response.status,
        hasAccessToken: !!response.data.access_token,
        hasUser: !!response.data.user,
        tokenType: response.data.token_type
      })

      // Store tokens in localStorage
      if (response.data.access_token) {
        localStorage.setItem('access_token', response.data.access_token)
      }
      if (response.data.refresh_token) {
        localStorage.setItem('refresh_token', response.data.refresh_token)
      }

      return response.data
    } catch (error) {
      console.error('AuthService: Login failed:', error)
      throw error
    }
  }

  /**
   * Logout user and clear authentication state
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout/', {})
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Logout successful')
      }
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with logout even if API call fails
    } finally {
      // Always clear tokens
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  /**
   * Verify current token and get user data
   */
  async verifyToken(): Promise<User> {
    try {
      const response = await apiClient.get<User>('/auth/user/')
      return response.data
    } catch (error) {
      console.warn('Token verification failed:', error)
      throw error
    }
  }

  /**
   * Register new user (if registration is enabled)
   */
  async register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
  }): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/register/', userData)

      // SECURITY FIX: No token storage needed - backend sets httpOnly cookies
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Registration successful - authentication cookies set by backend')
      }

      return response.data
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset/', { email })
    } catch (error) {
      console.error('Password reset request failed:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset-confirm/', {
        token,
        password: newPassword
      })
    } catch (error) {
      console.error('Password reset failed:', error)
      throw error
    }
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/change-password/', {
        current_password: currentPassword,
        new_password: newPassword
      })
    } catch (error) {
      console.error('Password change failed:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.patch<User>('/auth/profile/', profileData)
      return response.data
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    }
  }


}

// Create and export auth service instance
export const authService = new AuthService()

// Export for convenience
export default authService
