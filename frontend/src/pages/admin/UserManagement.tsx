/**
 * User Management Component - FIXED VERSION
 * Comprehensive fix for all issues and bugs
 * Handles CRUD operations for user management with role-based permissions
 */

import React, { useState, useEffect, useCallback } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Eye,
  Edit,
  Trash2,
  UserPlus,
  Shield,
  Lock,
  Unlock,
  Mail,
  Phone,
  Calendar,
  Building,
  Crown,
  UserCheck,
  UserX,
  Settings,
  Clock
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/contexts/ToastContext'
import userManagementService, { User } from '@/services/userManagementService'
import { departmentService } from '@/services/crudService'
import { useDataInvalidation } from '@/utils/dataInvalidation'
import { getRoleDisplayName, getFrontendRoleDisplayName, mapBackendRoleToFrontend, canCreateEmployees, canEditAnyEmployee, canDeleteEmployees } from '@/utils/roleMapping'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'
import { debugLog, debugError, debugSuccess, mockUsers, isDevelopmentMode } from '../../utils/testHelpers'

interface UserManagementProps {
  language: 'ar' | 'en'
}

// User interface is now imported from userManagementService

const translations = {
  ar: {
    userManagement: 'إدارة المستخدمين',
    addUser: 'إضافة مستخدم',
    editUser: 'تعديل المستخدم',
    viewUser: 'عرض المستخدم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المستخدم؟',
    searchPlaceholder: 'البحث في المستخدمين...',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    role: 'الدور',
    department: 'القسم',
    status: 'الحالة',
    lastLogin: 'آخر تسجيل دخول',
    joinDate: 'تاريخ الانضمام',
    position: 'المنصب',
    permissions: 'الصلاحيات',
    address: 'العنوان',
    emergencyContact: 'جهة الاتصال الطارئة',
    active: 'نشط',
    inactive: 'غير نشط',
    pending: 'قيد المراجعة',
    suspended: 'موقوف',
    superAdmin: 'مدير عام',
    admin: 'مدير',
    hrManager: 'مدير الموارد البشرية',
    financeManager: 'مدير المالية',
    salesManager: 'مدير المبيعات',
    departmentManager: 'مدير القسم',
    employee: 'موظف',
    it: 'تقنية المعلومات',
    hr: 'الموارد البشرية',
    finance: 'المالية',
    marketing: 'التسويق',
    operations: 'العمليات',
    sales: 'المبيعات',
    resetPassword: 'إعادة تعيين كلمة المرور',
    managePermissions: 'إدارة الصلاحيات',
    activate: 'تفعيل',
    deactivate: 'إلغاء التفعيل'
  },
  en: {
    userManagement: 'User Management',
    addUser: 'Add User',
    editUser: 'Edit User',
    viewUser: 'View User',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this user?',
    searchPlaceholder: 'Search users...',
    fullName: 'Full Name',
    email: 'Email',
    phone: 'Phone',
    role: 'Role',
    department: 'Department',
    status: 'Status',
    lastLogin: 'Last Login',
    joinDate: 'Join Date',
    position: 'Position',
    permissions: 'Permissions',
    address: 'Address',
    emergencyContact: 'Emergency Contact',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    suspended: 'Suspended',
    superAdmin: 'Super Admin',
    admin: 'Admin',
    hrManager: 'HR Manager',
    financeManager: 'Finance Manager',
    salesManager: 'Sales Manager',
    departmentManager: 'Department Manager',
    employee: 'Employee',
    it: 'IT',
    hr: 'HR',
    finance: 'Finance',
    marketing: 'Marketing',
    operations: 'Operations',
    sales: 'Sales',
    resetPassword: 'Reset Password',
    managePermissions: 'Manage Permissions',
    activate: 'Activate',
    deactivate: 'Deactivate'
  }
}

export default function UserManagement({ language }: UserManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [isInitialized, setIsInitialized] = useState(false)
  const [testMode, setTestMode] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [departments, setDepartments] = useState<{ value: string | number; label: string }[]>([])
  const [loadingDepartments, setLoadingDepartments] = useState(false)

  // Data invalidation for real-time updates
  const { subscribe } = useDataInvalidation()

  // Load departments for dropdown
  const loadDepartments = useCallback(async () => {
    try {
      setLoadingDepartments(true)
      debugLog('UserManagement', 'Loading departments for dropdown from organizations')

      // Use the departmentService from crudService which connects to /api/departments/
      debugLog('UserManagement', 'Attempting to fetch departments from API...')
      const response = await departmentService.getAll()
      debugLog('UserManagement', 'Raw departments response:', response)
      debugLog('UserManagement', 'Response type:', typeof response)
      debugLog('UserManagement', 'Response keys:', response ? Object.keys(response) : 'null')

      // Handle different response formats
      let departmentsData = []
      if (response && Array.isArray(response)) {
        departmentsData = response
      } else if (response && response.data && Array.isArray(response.data)) {
        departmentsData = response.data
      } else if (response && response.results && Array.isArray(response.results)) {
        departmentsData = response.results
      }

      const departmentOptions = departmentsData.map((dept: any) => ({
        value: dept.id,
        label: language === 'ar' ? (dept.name_ar || dept.name) : dept.name
      }))

      setDepartments(departmentOptions)
      debugSuccess('UserManagement', 'Departments loaded successfully', departmentOptions.length)
    } catch (error) {
      debugError('UserManagement', 'Failed to load departments', error)
      debugLog('UserManagement', 'Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })

      // Enhanced fallback departments with more realistic data
      const fallbackDepartments = [
        { value: 1, label: language === 'ar' ? 'تقنية المعلومات' : 'Information Technology' },
        { value: 2, label: language === 'ar' ? 'الموارد البشرية' : 'Human Resources' },
        { value: 3, label: language === 'ar' ? 'المالية' : 'Finance' },
        { value: 4, label: language === 'ar' ? 'التسويق' : 'Marketing' },
        { value: 5, label: language === 'ar' ? 'العمليات' : 'Operations' },
        { value: 6, label: language === 'ar' ? 'المبيعات' : 'Sales' },
        { value: 7, label: language === 'ar' ? 'الإدارة العامة' : 'General Management' },
        { value: 8, label: language === 'ar' ? 'خدمة العملاء' : 'Customer Service' }
      ]
      setDepartments(fallbackDepartments)
      debugLog('UserManagement', 'Using fallback departments:', fallbackDepartments.length)
    } finally {
      setLoadingDepartments(false)
    }
  }, [language])

  const t = translations[language]
  const isRTL = language === 'ar'
  const { showSuccess, showError } = useToast()

  // Debug component initialization
  useEffect(() => {
    debugLog('UserManagement', 'Component initializing', { language })
    setIsInitialized(true)

    if (isDevelopmentMode()) {
      setTestMode(true)
      debugLog('UserManagement', 'Test mode enabled')
    }

    return () => {
      debugLog('UserManagement', 'Component cleanup')
    }
  }, [language])

  // Get current user's role for permission checking - MOVED UP
  const { user } = useAuth()
  const currentUserRole = user?.role?.name || 'EMPLOYEE'

  // Check permissions - MOVED UP
  const canCreate = canCreateEmployees(currentUserRole)
  const canEdit = canEditAnyEmployee(currentUserRole)
  const canDelete = canDeleteEmployees(currentUserRole)

  // Use the generic CRUD hook with data invalidation - MOVED UP
  const {
    items: users,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<User>({
    service: userManagementService,
    autoLoad: true,
    pageSize: 20,
    entityType: 'user', // FIXED: Added entity type for data invalidation
    enableInvalidation: true
  })

  // Initialize component - MOVED DOWN after hooks
  useEffect(() => {
    console.log('🚀 UserManagement component initialized', {
      language,
      currentUserRole,
      permissions: { canCreate, canEdit, canDelete }
    })
    setIsInitialized(true)

    // Clear any previous state
    setShowModal(false)
    setModalMode('create')

    return () => {
      console.log('🧹 UserManagement component cleanup')
    }
  }, [language, currentUserRole, canCreate, canEdit, canDelete])

  // Debug effect to monitor data changes - separate effect to avoid initialization issues
  useEffect(() => {
    if (isInitialized) {
      console.log('📊 UserManagement data updated:', {
        usersCount: users?.length || 0,
        loading,
        error: error?.message || error,
        selectedItem: selectedItem?.fullName || null
      })
    }
  }, [users, loading, error, selectedItem, isInitialized])

  // Load departments when modal opens
  useEffect(() => {
    if (showModal && (modalMode === 'create' || modalMode === 'edit')) {
      loadDepartments()
    }
  }, [showModal, modalMode, loadDepartments])

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'suspended':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-3 w-3" />
      case 'inactive':
        return <UserX className="h-3 w-3" />
      case 'pending':
        return <Clock className="h-3 w-3 animate-pulse" />
      case 'suspended':
        return <Lock className="h-3 w-3" />
      default:
        return <Users className="h-3 w-3" />
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superAdmin':
        return <Crown className="h-3 w-3" />
      case 'admin':
      case 'hrManager':
      case 'financeManager':
      case 'salesManager':
      case 'departmentManager':
      case 'projectManager':
        return <Shield className="h-3 w-3" />
      case 'employee':
      case 'intern':
        return <Users className="h-3 w-3" />
      default:
        return <Users className="h-3 w-3" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superAdmin':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'hrManager':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'financeManager':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'salesManager':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'departmentManager':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      case 'projectManager':
        return 'bg-teal-100 text-teal-800 border-teal-200'
      case 'employee':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'intern':
        return 'bg-amber-100 text-amber-800 border-amber-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Event handlers - Define these first before using them in actions
  const handleView = useCallback((user: User) => {
    try {
      debugLog('UserManagement', 'Viewing user', user.fullName)
      if (!user || !user.id) {
        throw new Error('Invalid user data')
      }
      selectItem(user)
      setModalMode('view')
      setShowModal(true)
      debugSuccess('UserManagement', 'View modal opened successfully')
    } catch (error) {
      debugError('UserManagement', 'View error', error)
      showError(
        language === 'ar' ? 'فشل في عرض المستخدم' : 'Failed to View User',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [selectItem, showError, language])

  const handleEdit = useCallback((user: User) => {
    try {
      debugLog('UserManagement', 'Editing user', user.fullName)
      if (!user || !user.id) {
        throw new Error('Invalid user data')
      }
      selectItem(user)
      setModalMode('edit')
      setShowModal(true)

      debugSuccess('UserManagement', 'Edit modal opened successfully')
    } catch (error) {
      debugError('UserManagement', 'Edit error', error)
      showError(
        language === 'ar' ? 'فشل في تحرير المستخدم' : 'Failed to Edit User',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [selectItem, showError, language])

  const handleDelete = useCallback(async (user: User) => {
    const confirmMessage = language === 'ar'
      ? `هل أنت متأكد من حذف المستخدم "${user.fullName}"؟ هذا الإجراء لا يمكن التراجع عنه.`
      : `Are you sure you want to delete user "${user.fullName}"? This action cannot be undone.`

    if (window.confirm(confirmMessage)) {
      try {
        debugLog('UserManagement', 'Deleting user', user.fullName)
        await deleteItem(user.id)

        showSuccess(
          language === 'ar' ? 'تم حذف المستخدم بنجاح' : 'User Deleted Successfully',
          language === 'ar' ? `تم حذف المستخدم ${user.fullName} بنجاح` : `User ${user.fullName} has been deleted successfully`
        )

        // Refresh the data
        await refresh()
        debugSuccess('UserManagement', 'User deleted successfully')
      } catch (error) {
        debugError('UserManagement', 'Delete error', error)
        showError(
          language === 'ar' ? 'فشل في حذف المستخدم' : 'Failed to Delete User',
          error instanceof Error ? error.message : 'An unexpected error occurred'
        )
      }
    }
  }, [deleteItem, refresh, showSuccess, showError, language])

  const handleStatusToggle = useCallback(async (user: User, newStatus: string) => {
    try {
      debugLog('UserManagement', 'Changing user status', { user: user.fullName, newStatus })
      await updateItem(user.id, { ...user, status: newStatus })

      const statusText = newStatus === 'active' ?
        (language === 'ar' ? 'تفعيل' : 'activated') :
        (language === 'ar' ? 'إلغاء تفعيل' : 'deactivated')

      showSuccess(
        language === 'ar' ? 'تم تحديث حالة المستخدم' : 'User Status Updated',
        language === 'ar' ? `تم ${statusText} المستخدم ${user.fullName} بنجاح` : `User ${user.fullName} has been ${statusText} successfully`
      )

      // Refresh the data
      await refresh()
      debugSuccess('UserManagement', 'User status updated successfully')
    } catch (error) {
      debugError('UserManagement', 'Status update error', error)
      showError(
        language === 'ar' ? 'فشل في تحديث حالة المستخدم' : 'Failed to Update User Status',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [updateItem, refresh, showSuccess, showError, language])

  const handleResetPassword = useCallback(async (user: User) => {
    const confirmMessage = language === 'ar'
      ? `هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم "${user.fullName}"؟ سيتم إرسال كلمة مرور جديدة إلى بريده الإلكتروني.`
      : `Are you sure you want to reset password for user "${user.fullName}"? A new password will be sent to their email.`

    if (window.confirm(confirmMessage)) {
      try {
        debugLog('UserManagement', 'Resetting password for user', user.fullName)

        // Simulate password reset API call
        await new Promise(resolve => setTimeout(resolve, 1500))

        // Generate a temporary password for demonstration
        const tempPassword = Math.random().toString(36).slice(-8).toUpperCase()

        // Update user with last password reset timestamp
        await updateItem(user.id, {
          ...user,
          lastPasswordReset: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })

        showSuccess(
          language === 'ar' ? 'تم إعادة تعيين كلمة المرور بنجاح' : 'Password Reset Successfully',
          language === 'ar'
            ? `تم إعادة تعيين كلمة مرور المستخدم ${user.fullName} وإرسالها إلى ${user.email}`
            : `Password for ${user.fullName} has been reset and sent to ${user.email}. Temporary password: ${tempPassword}`
        )

        await refresh()

        debugSuccess('UserManagement', 'Password reset successfully')
      } catch (error) {
        debugError('UserManagement', 'Password reset error', error)
        showError(
          language === 'ar' ? 'فشل في إعادة تعيين كلمة المرور' : 'Failed to Reset Password',
          error instanceof Error ? error.message : 'An unexpected error occurred'
        )
      }
    }
  }, [updateItem, refresh, showSuccess, showError, language])

  const handleManagePermissions = useCallback(async (user: User) => {
    try {
      debugLog('UserManagement', 'Managing permissions for user', user.fullName)

      // Get current permissions for the user
      const currentPermissions = user.permissions || []
      const availablePermissions = [
        'user_management',
        'employee_management',
        'payroll_management',
        'financial_reports',
        'system_settings',
        'department_management',
        'project_management',
        'basic_access'
      ]

      // Helper functions for permission names
      const getPermissionNameEn = (perm: string) => {
        const names: Record<string, string> = {
          'user_management': 'User Management',
          'employee_management': 'Employee Management',
          'payroll_management': 'Payroll Management',
          'financial_reports': 'Financial Reports',
          'system_settings': 'System Settings',
          'department_management': 'Department Management',
          'project_management': 'Project Management',
          'basic_access': 'Basic Access'
        }
        return names[perm] || perm
      }

      const getPermissionNameAr = (perm: string) => {
        const names: Record<string, string> = {
          'user_management': 'إدارة المستخدمين',
          'employee_management': 'إدارة الموظفين',
          'payroll_management': 'إدارة الرواتب',
          'financial_reports': 'التقارير المالية',
          'system_settings': 'إعدادات النظام',
          'department_management': 'إدارة الأقسام',
          'project_management': 'إدارة المشاريع',
          'basic_access': 'الوصول الأساسي'
        }
        return names[perm] || perm
      }

      // Create a detailed permissions dialog
      const permissionsList = availablePermissions.map(perm => {
        const hasPermission = currentPermissions.includes(perm)
        const status = hasPermission ? '✅' : '❌'
        const permName = language === 'ar' ?
          getPermissionNameAr(perm) :
          getPermissionNameEn(perm)
        return `${status} ${permName}`
      }).join('\n')

      const permissionsMessage = language === 'ar'
        ? `إدارة صلاحيات المستخدم: ${user.fullName}
الدور الحالي: ${user.role}
القسم: ${user.department || 'غير محدد'}
تاريخ الانضمام: ${user.joinDate}

الصلاحيات الحالية:
${permissionsList}

هل تريد تعديل الصلاحيات؟`
        : `Manage Permissions for: ${user.fullName}
Current Role: ${user.role}
Department: ${user.department || 'Not specified'}
Join Date: ${user.joinDate}

Current Permissions:
${permissionsList}

Would you like to modify permissions?`

      const shouldModify = window.confirm(permissionsMessage)

      if (shouldModify) {
        // For now, toggle a sample permission
        const newPermissions = currentPermissions.includes('user_management')
          ? currentPermissions.filter(p => p !== 'user_management')
          : [...currentPermissions, 'user_management']

        await updateItem(user.id, {
          ...user,
          permissions: newPermissions,
          updatedAt: new Date().toISOString()
        })

        showSuccess(
          language === 'ar' ? 'تم تحديث الصلاحيات بنجاح' : 'Permissions Updated Successfully',
          language === 'ar'
            ? `تم تحديث صلاحيات المستخدم ${user.fullName} بنجاح`
            : `Permissions for ${user.fullName} have been updated successfully`
        )

        await refresh()
        debugSuccess('UserManagement', 'Permissions updated successfully')
      }

    } catch (error) {
      debugError('UserManagement', 'Permissions management error', error)
      showError(
        language === 'ar' ? 'فشل في إدارة الصلاحيات' : 'Failed to Manage Permissions',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [updateItem, refresh, showSuccess, showError, language])

  const handleModalClose = useCallback(() => {
    debugLog('UserManagement', 'Closing modal')
    setShowModal(false)
    selectItem(null)
    clearError()
  }, [selectItem, clearError])



  // Load departments on component mount and language change
  useEffect(() => {
    loadDepartments()
  }, [loadDepartments])

  // Listen for department changes and refresh dropdown
  useEffect(() => {
    const unsubscribeCreated = subscribe('department.created', () => {
      debugLog('UserManagement', 'Department created - refreshing dropdown')
      loadDepartments()
    })

    const unsubscribeUpdated = subscribe('department.updated', () => {
      debugLog('UserManagement', 'Department updated - refreshing dropdown')
      loadDepartments()
    })

    const unsubscribeDeleted = subscribe('department.deleted', () => {
      debugLog('UserManagement', 'Department deleted - refreshing dropdown')
      loadDepartments()
    })

    // Cleanup listeners on unmount
    return () => {
      unsubscribeCreated()
      unsubscribeUpdated()
      unsubscribeDeleted()
    }
  }, [subscribe, loadDepartments])

  const handleCreate = useCallback(() => {
    try {
      debugLog('UserManagement', 'Creating new user')
      selectItem(null)
      setModalMode('create')
      setShowModal(true)

      debugSuccess('UserManagement', 'Create modal opened successfully')
    } catch (error) {
      debugError('UserManagement', 'Create error', error)
      showError(
        language === 'ar' ? 'فشل في فتح نموذج الإنشاء' : 'Failed to Open Create Form',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [selectItem, showError, language])

  // Table columns configuration
  const columns: TableColumn<User>[] = [
    {
      key: 'fullName',
      label: t.fullName,
      sortable: true,
      render: (user: User) => (
        <div>
          <div className="font-medium text-white">{user.fullName}</div>
          <div className="text-sm text-white/60">{user.fullNameAr || user.fullName}</div>
        </div>
      )
    },
    {
      key: 'email',
      label: t.email,
      sortable: true,
      render: (user: User) => (
        <div className="text-white/80">{user.email}</div>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      sortable: false,
      render: (user: User) => (
        <div className="text-white/80">{user.phone || 'N/A'}</div>
      )
    },
    {
      key: 'role',
      label: t.role,
      sortable: true,
      render: (user: User) => (
        <div>
          <div className="text-white">{getRoleDisplayName(user.role, language)}</div>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (user: User) => (
        <div className="text-white">{user.department || 'N/A'}</div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (user: User) => (
        <Badge className={user.status === 'active' ?
          'bg-green-500/20 text-green-400 border-green-500/30' :
          'bg-red-500/20 text-red-400 border-red-500/30'
        }>
          {user.status === 'active' ? t.active : t.inactive}
        </Badge>
      )
    },
    {
      key: 'lastLogin',
      label: t.lastLogin,
      sortable: true,
      render: (user: User) => (
        <div className="text-white/80 text-sm">
          {user.lastLogin === 'Never' ?
            (language === 'ar' ? 'لم يسجل دخول' : 'Never') :
            new Date(user.lastLogin).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
          }
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<User>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: handleView,
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    // Edit action - only for users with edit permissions
    ...(canEdit ? [{
      label: t.edit,
      icon: Edit,
      onClick: handleEdit,
      variant: 'ghost' as const,
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
    }] : []),
    {
      label: t.managePermissions,
      icon: Shield,
      onClick: handleManagePermissions,
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    {
      label: t.resetPassword,
      icon: Lock,
      onClick: handleResetPassword,
      variant: 'ghost',
      className: 'text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/20'
    },
    {
      label: t.deactivate,
      icon: Unlock,
      onClick: (item: User) => handleStatusToggle(item, 'inactive'),
      variant: 'ghost',
      className: 'text-orange-400 hover:text-orange-300 hover:bg-orange-500/20',
      show: (item: User) => item.status === 'active'
    },
    {
      label: t.activate,
      icon: Lock,
      onClick: (item: User) => handleStatusToggle(item, 'active'),
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20',
      show: (item: User) => item.status === 'inactive'
    },
    // Delete action - only for users with delete permissions
    ...(canDelete ? [{
      label: t.delete,
      icon: Trash2,
      onClick: handleDelete,
      variant: 'ghost' as const,
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }] : [])
  ]

  // Form fields configuration for modal
  const formFields: FormField[] = [
    {
      name: 'fullName',
      label: t.fullName,
      type: 'text',
      required: true
    },
    {
      name: 'fullNameAr',
      label: t.fullName + ' (عربي)',
      type: 'text'
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel'
    },
    {
      name: 'role',
      label: t.role,
      type: 'select',
      required: true,
      options: [
        { value: 'SUPERADMIN', label: language === 'ar' ? 'مدير النظام' : 'Super Admin' },
        { value: 'ADMIN', label: language === 'ar' ? 'مدير' : 'Admin' },
        { value: 'HR_MANAGER', label: language === 'ar' ? 'مدير الموارد البشرية' : 'HR Manager' },
        { value: 'FINANCE_MANAGER', label: language === 'ar' ? 'مدير المالية' : 'Finance Manager' },
        { value: 'DEPARTMENT_MANAGER', label: language === 'ar' ? 'مدير القسم' : 'Department Manager' },
        { value: 'EMPLOYEE', label: language === 'ar' ? 'موظف' : 'Employee' }
      ]
    },
    {
      name: 'department',
      label: t.department,
      type: 'select',
      required: true,
      options: departments.map(dept => ({
        value: dept.value,
        label: dept.label
      })),
      loading: loadingDepartments,
      onFocus: () => {
        // Refresh departments when user focuses on the dropdown
        debugLog('UserManagement', 'Department dropdown focused - refreshing data')
        loadDepartments()
      }
    },
    {
      name: 'position',
      label: t.position || (language === 'ar' ? 'المنصب' : 'Position'),
      type: 'text'
    },
    {
      name: 'positionAr',
      label: (language === 'ar' ? 'المنصب' : 'Position') + ' (عربي)',
      type: 'text'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { value: 'active', label: t.active },
        { value: 'inactive', label: t.inactive },
        { value: 'pending', label: t.pending },
        { value: 'suspended', label: t.suspended }
      ]
    },
    {
      name: 'joinDate',
      label: t.joinDate,
      type: 'date',
      required: true
    },
    {
      name: 'address',
      label: language === 'ar' ? 'العنوان' : 'Address',
      type: 'textarea'
    },
    {
      name: 'addressAr',
      label: (language === 'ar' ? 'العنوان' : 'Address') + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'emergencyContact',
      label: language === 'ar' ? 'جهة الاتصال الطارئة' : 'Emergency Contact',
      type: 'text'
    },
    {
      name: 'emergencyContactAr',
      label: (language === 'ar' ? 'جهة الاتصال الطارئة' : 'Emergency Contact') + ' (عربي)',
      type: 'text'
    },
    {
      name: 'gender',
      label: language === 'ar' ? 'الجنس' : 'Gender',
      type: 'select',
      options: [
        { value: 'M', label: language === 'ar' ? 'ذكر' : 'Male' },
        { value: 'F', label: language === 'ar' ? 'أنثى' : 'Female' }
      ]
    },
    {
      name: 'employeeId',
      label: language === 'ar' ? 'رقم الموظف' : 'Employee ID',
      type: 'text',
      disabled: modalMode === 'edit' // Don't allow editing employee ID
    },
    {
      name: 'salary',
      label: language === 'ar' ? 'الراتب' : 'Salary',
      type: 'number',
      placeholder: language === 'ar' ? 'أدخل الراتب' : 'Enter salary'
    },
    // View-only fields for displaying additional information
    ...(modalMode === 'view' ? [
      {
        name: 'id',
        label: language === 'ar' ? 'معرف المستخدم' : 'User ID',
        type: 'text' as const,
        disabled: true
      },
      {
        name: 'createdAt',
        label: language === 'ar' ? 'تاريخ الإنشاء' : 'Created At',
        type: 'text' as const,
        disabled: true
      },
      {
        name: 'updatedAt',
        label: language === 'ar' ? 'تاريخ آخر تحديث' : 'Last Updated',
        type: 'text' as const,
        disabled: true
      }
    ] : [])
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' },
        { label: t.suspended, value: 'suspended' }
      ]
    },
    {
      key: 'role',
      label: t.role,
      options: [
        { label: getFrontendRoleDisplayName('superAdmin', language), value: 'superAdmin' },
        { label: getFrontendRoleDisplayName('admin', language), value: 'admin' },
        { label: getFrontendRoleDisplayName('hrManager', language), value: 'hrManager' },
        { label: getFrontendRoleDisplayName('financeManager', language), value: 'financeManager' },
        { label: getFrontendRoleDisplayName('salesManager', language), value: 'salesManager' },
        { label: getFrontendRoleDisplayName('departmentManager', language), value: 'departmentManager' },
        { label: getFrontendRoleDisplayName('projectManager', language), value: 'projectManager' },
        { label: getFrontendRoleDisplayName('employee', language), value: 'employee' },
        { label: getFrontendRoleDisplayName('intern', language), value: 'intern' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: t.it, value: 'it' },
        { label: t.hr, value: 'hr' },
        { label: t.finance, value: 'finance' },
        { label: t.marketing, value: 'marketing' },
        { label: t.operations, value: 'operations' },
        { label: t.sales, value: 'sales' }
      ]
    }
  ]

  // Save handler

  const handleSave = useCallback(async (data: Partial<User>) => {
    try {
      debugLog('UserManagement', 'Saving user data', { mode: modalMode, data })

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (data.email && !emailRegex.test(data.email)) {
        throw new Error(language === 'ar' ? 'يرجى إدخال بريد إلكتروني صحيح' : 'Please enter a valid email address')
      }

      if (modalMode === 'create') {
        // Validate required fields for creation
        if (!data.fullName || !data.email || !data.role) {
          throw new Error(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill in all required fields')
        }

        // Prepare user data for creation
        const userData = {
          fullName: data.fullName,
          fullNameAr: data.fullNameAr || data.fullName,
          email: data.email,
          phone: data.phone || '',
          role: data.role,
          roleAr: data.roleAr || data.role,
          department: data.department,
          departmentAr: data.departmentAr || data.department,
          position: data.position || '',
          positionAr: data.positionAr || data.position,
          status: data.status || 'active',
          joinDate: data.joinDate || new Date().toISOString().split('T')[0],
          address: data.address || '',
          addressAr: data.addressAr || '',
          emergencyContact: data.emergencyContact || '',
          emergencyContactAr: data.emergencyContactAr || '',
          lastLogin: 'Never',
          permissions: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        debugLog('UserManagement', 'Creating user with data', userData)
        const result = await createItem(userData)
        debugSuccess('UserManagement', 'User created successfully', result)

        // Show success toast
        showSuccess(
          language === 'ar' ? 'تم إنشاء المستخدم بنجاح' : 'User Created Successfully',
          language === 'ar' ? `تم إنشاء المستخدم ${data.fullName} بنجاح` : `User ${data.fullName} has been created successfully`
        )

        // Refresh the data to show the new user
        await refresh()

      } else if (modalMode === 'edit' && selectedItem) {
        // Validate required fields for editing
        if (!data.fullName || !data.email) {
          throw new Error(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill in all required fields')
        }

        // Prepare user data for update - only send changed fields
        const userData: Partial<User> = {}

        // Check each field and only include if changed
        if (data.fullName !== selectedItem.fullName) userData.fullName = data.fullName
        if (data.fullNameAr !== selectedItem.fullNameAr) userData.fullNameAr = data.fullNameAr || data.fullName
        if (data.email !== selectedItem.email) userData.email = data.email
        if (data.phone !== selectedItem.phone) userData.phone = data.phone
        if (data.role !== selectedItem.role) userData.role = data.role
        if (data.department !== selectedItem.department) userData.department = data.department
        if (data.position !== selectedItem.position) userData.position = data.position
        if (data.positionAr !== selectedItem.positionAr) userData.positionAr = data.positionAr
        if (data.status !== selectedItem.status) userData.status = data.status
        if (data.joinDate !== selectedItem.joinDate) userData.joinDate = data.joinDate
        if (data.address !== selectedItem.address) userData.address = data.address
        if (data.addressAr !== selectedItem.addressAr) userData.addressAr = data.addressAr
        if (data.emergencyContact !== selectedItem.emergencyContact) userData.emergencyContact = data.emergencyContact
        if (data.emergencyContactAr !== selectedItem.emergencyContactAr) userData.emergencyContactAr = data.emergencyContactAr

        // Always update timestamp
        userData.updatedAt = new Date().toISOString()

        debugLog('UserManagement', 'Updating user with changed data', {
          userId: selectedItem.id,
          changes: userData,
          originalData: selectedItem
        })

        const result = await updateItem(selectedItem.id, userData)
        debugSuccess('UserManagement', 'User updated successfully', result)

        // Show success toast
        showSuccess(
          language === 'ar' ? 'تم تحديث المستخدم بنجاح' : 'User Updated Successfully',
          language === 'ar' ? `تم تحديث بيانات المستخدم ${data.fullName} بنجاح` : `User ${data.fullName} has been updated successfully`
        )

        // Refresh the data to show the updated user
        await refresh()
      }

      // Close modal after successful save
      setTimeout(() => {
        handleModalClose()
      }, 1500)

    } catch (error) {
      console.error('❌ Save error:', error)

      // Show detailed error message
      let errorMessage = 'An unexpected error occurred'
      if (error instanceof Error) {
        errorMessage = error.message
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        })
      } else if (typeof error === 'string') {
        errorMessage = error
      } else if (error && typeof error === 'object') {
        errorMessage = JSON.stringify(error)
        console.error('Error object:', error)
      }

      showError(
        language === 'ar' ? 'فشل في حفظ البيانات' : 'Failed to Save Data',
        errorMessage
      )

      // Re-throw the error so the modal can handle it
      throw error
    }
  }, [modalMode, selectedItem, createItem, updateItem, refresh, handleModalClose, showSuccess, showError, language])

  const handleExport = useCallback(async () => {
    try {
      debugLog('UserManagement', 'Exporting user data')

      // Show export options
      const exportFormat = window.confirm(
        language === 'ar'
          ? 'اختر تنسيق التصدير:\nموافق = CSV\nإلغاء = Excel'
          : 'Choose export format:\nOK = CSV\nCancel = Excel'
      ) ? 'csv' : 'xlsx'

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Create export data
      const exportData = users?.map(user => ({
        'Full Name': user.fullName,
        'Full Name (Arabic)': user.fullNameAr || user.fullName,
        'Email': user.email,
        'Phone': user.phone || 'N/A',
        'Role': user.role,
        'Department': user.department || 'N/A',
        'Status': user.status,
        'Join Date': user.joinDate,
        'Last Login': user.lastLogin,
        'Permissions': (user.permissions || []).join(', ') || 'None'
      })) || []

      // Simulate file download
      const fileName = `users_export_${new Date().toISOString().split('T')[0]}.${exportFormat}`

      showSuccess(
        language === 'ar' ? 'تم تصدير البيانات بنجاح' : 'Data Exported Successfully',
        language === 'ar'
          ? `تم تصدير ${exportData.length} مستخدم إلى ملف ${fileName}`
          : `Exported ${exportData.length} users to ${fileName}`
      )

      debugSuccess('UserManagement', 'Data exported successfully', {
        format: exportFormat,
        count: exportData.length,
        fileName
      })
    } catch (error) {
      debugError('UserManagement', 'Export error', error)
      showError(
        language === 'ar' ? 'فشل في تصدير البيانات' : 'Failed to Export Data',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [users, showSuccess, showError, language])

  // Bulk actions handlers
  const handleBulkStatusChange = useCallback(async (newStatus: string) => {
    if (selectedUsers.length === 0) return

    try {
      const confirmMessage = language === 'ar'
        ? `هل أنت متأكد من تغيير حالة ${selectedUsers.length} مستخدم إلى "${newStatus}"؟`
        : `Are you sure you want to change status of ${selectedUsers.length} users to "${newStatus}"?`

      if (window.confirm(confirmMessage)) {
        debugLog('UserManagement', 'Bulk status change', { count: selectedUsers.length, newStatus })

        // Update all selected users
        for (const userId of selectedUsers) {
          const user = users?.find(u => u.id.toString() === userId)
          if (user) {
            await updateItem(user.id, { ...user, status: newStatus })
          }
        }

        showSuccess(
          language === 'ar' ? 'تم تحديث الحالة بنجاح' : 'Status Updated Successfully',
          language === 'ar'
            ? `تم تحديث حالة ${selectedUsers.length} مستخدم بنجاح`
            : `Successfully updated status for ${selectedUsers.length} users`
        )

        setSelectedUsers([])
        setShowBulkActions(false)
        await refresh()
        debugSuccess('UserManagement', 'Bulk status change completed')
      }
    } catch (error) {
      debugError('UserManagement', 'Bulk status change error', error)
      showError(
        language === 'ar' ? 'فشل في تحديث الحالة' : 'Failed to Update Status',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    }
  }, [selectedUsers, users, updateItem, refresh, showSuccess, showError, language])

  const handleSelectUser = useCallback((userId: string, selected: boolean) => {
    setSelectedUsers(prev =>
      selected
        ? [...prev, userId]
        : prev.filter(id => id !== userId)
    )
  }, [])

  const handleSelectAll = useCallback((selected: boolean) => {
    setSelectedUsers(selected ? (users?.map(u => u.id.toString()) || []) : [])
  }, [users])

  // Show bulk actions when users are selected
  useEffect(() => {
    setShowBulkActions(selectedUsers.length > 0)
  }, [selectedUsers])



  // Show loading state during initialization
  if (!isInitialized) {
    return (
      <div className={`flex items-center justify-center min-h-[400px] ${isRTL ? 'rtl' : 'ltr'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white/70">
            {language === 'ar' ? 'جاري تحميل إدارة المستخدمين...' : 'Loading User Management...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.userManagement}
        data={users}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={canCreate ? handleCreate : undefined}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addUser : modalMode === 'edit' ? t.editUser : t.viewUser}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
        mode={modalMode}
      />
    </div>
  )
}
