/**
 * Customers Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Phone,
  Mail,
  Calendar,
  DollarSign,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { customerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CustomersProps {
  language: 'ar' | 'en'
}

interface Customer {
  id: string
  customer_id: string
  first_name: string
  last_name: string
  full_name?: string
  email: string
  phone?: string
  customer_type: 'individual' | 'business' | 'enterprise'
  status: 'active' | 'inactive' | 'suspended' | 'prospect'
  company_name?: string
  company_size?: string
  industry?: string
  address_line1?: string
  address_line2?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  source?: string
  internal_notes?: string
  tags?: string
  total_orders: number
  total_spent: number
  satisfaction_score: number
  created_at: string
  updated_at: string
  last_contact_date?: string
}

const translations = {
  ar: {
    customers: 'العملاء',
    addCustomer: 'إضافة عميل',
    editCustomer: 'تعديل العميل',
    searchCustomers: 'البحث في العملاء',
    name: 'اسم العميل',
    nameAr: 'الاسم بالعربية',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    company: 'الشركة',
    companyAr: 'الشركة بالعربية',
    address: 'العنوان',
    addressAr: 'العنوان بالعربية',
    customerType: 'نوع العميل',
    status: 'الحالة',
    totalOrders: 'إجمالي الطلبات',
    totalValue: 'إجمالي القيمة',
    lastOrderDate: 'تاريخ آخر طلب',
    joinDate: 'تاريخ الانضمام',
    rating: 'التقييم',
    createdAt: 'تاريخ الإنشاء',
    actions: 'الإجراءات',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    active: 'نشط',
    inactive: 'غير نشط',
    individual: 'فرد',
    corporate: 'شركة',
    vip: 'عميل مميز',
    searchPlaceholder: 'البحث بالاسم أو البريد الإلكتروني...',
    noCustomers: 'لا يوجد عملاء',
    loading: 'جاري التحميل...',
    manageCustomers: 'إدارة العملاء والعلاقات',
    createSuccess: 'تم إنشاء العميل بنجاح',
    updateSuccess: 'تم تحديث العميل بنجاح',
    deleteSuccess: 'تم حذف العميل بنجاح',
    confirmDelete: 'هل أنت متأكد من حذف هذا العميل؟',
    totalSpent: 'إجمالي المبلغ المنفق',
    firstName: 'الاسم الأول',
    lastName: 'الاسم الأخير',
    companySize: 'حجم الشركة',
    industry: 'الصناعة',
    addressLine2: 'العنوان الثاني',
    city: 'المدينة',
    state: 'الولاية',
    postalCode: 'الرمز البريدي',
    country: 'البلد',
    business: 'تجاري',
    enterprise: 'مؤسسة',
    suspended: 'معلق',
    prospect: 'محتمل',
    all: 'الكل'
  },
  en: {
    customers: 'Customers',
    addCustomer: 'Add Customer',
    editCustomer: 'Edit Customer',
    searchCustomers: 'Search Customers',
    name: 'Customer Name',
    nameAr: 'Name (Arabic)',
    email: 'Email',
    phone: 'Phone',
    company: 'Company',
    companyAr: 'Company (Arabic)',
    address: 'Address',
    addressAr: 'Address (Arabic)',
    customerType: 'Customer Type',
    status: 'Status',
    totalOrders: 'Total Orders',
    totalValue: 'Total Value',
    lastOrderDate: 'Last Order Date',
    joinDate: 'Join Date',
    rating: 'Rating',
    createdAt: 'Created Date',
    actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    active: 'Active',
    inactive: 'Inactive',
    individual: 'Individual',
    corporate: 'Corporate',
    vip: 'VIP',
    searchPlaceholder: 'Search by name or email...',
    noCustomers: 'No customers found',
    loading: 'Loading...',
    manageCustomers: 'Manage customers and relationships',
    createSuccess: 'Customer created successfully',
    updateSuccess: 'Customer updated successfully',
    deleteSuccess: 'Customer deleted successfully',
    confirmDelete: 'Are you sure you want to delete this customer?',
    totalSpent: 'Total Spent',
    firstName: 'First Name',
    lastName: 'Last Name',
    companySize: 'Company Size',
    industry: 'Industry',
    addressLine2: 'Address Line 2',
    city: 'City',
    state: 'State',
    postalCode: 'Postal Code',
    country: 'Country',
    business: 'Business',
    enterprise: 'Enterprise',
    suspended: 'Suspended',
    prospect: 'Prospect',
    all: 'All'
  }
}

export default function Customers({ language }: CustomersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: customers,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Customer>({
    service: customerService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string | null | undefined) => {
    if (!status || typeof status !== 'string') {
      return 'bg-gray-500/20 text-gray-300'
    }

    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-500/20 text-green-300'
      case 'inactive':
        return 'bg-red-500/20 text-red-300'
      case 'suspended':
        return 'bg-orange-500/20 text-orange-300'
      case 'prospect':
        return 'bg-yellow-500/20 text-yellow-300'
      default:
        return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getTypeColor = (type: string | null | undefined) => {
    if (!type || typeof type !== 'string') {
      return 'bg-gray-500/20 text-gray-300'
    }

    switch (type.toLowerCase()) {
      case 'individual':
        return 'bg-blue-500/20 text-blue-300'
      case 'business':
        return 'bg-purple-500/20 text-purple-300'
      case 'enterprise':
        return 'bg-yellow-500/20 text-yellow-300'
      default:
        return 'bg-gray-500/20 text-gray-300'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<Customer>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Customer) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <Users className="h-5 w-5 text-white" />
          </div>
          <div>
            <div className="font-medium text-white">
              {item?.full_name || `${item?.first_name || ''} ${item?.last_name || ''}`.trim() || '-'}
            </div>
            <div className="text-sm text-white/60">
              {item?.company_name || '-'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      label: t.email,
      sortable: true,
      render: (item: Customer) => (
        <div className="flex items-center gap-2 text-white/90">
          <Mail className="h-4 w-4 text-white/60" />
          <span>{item?.email || '-'}</span>
        </div>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: Customer) => (
        <div className="flex items-center gap-2 text-white/90">
          <Phone className="h-4 w-4 text-white/60" />
          <span>{item?.phone || '-'}</span>
        </div>
      )
    },
    {
      key: 'customer_type',
      label: t.customerType,
      sortable: true,
      align: 'center' as const,
      render: (item: Customer) => (
        <Badge className={getTypeColor(item?.customer_type)}>
          {item?.customer_type ? (t[item.customer_type.toLowerCase() as keyof typeof t] || item.customer_type) : '-'}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      align: 'center' as const,
      render: (item: Customer) => (
        <Badge className={getStatusColor(item?.status)}>
          {item?.status ? (t[item.status.toLowerCase() as keyof typeof t] || item.status) : '-'}
        </Badge>
      )
    },
    {
      key: 'total_spent',
      label: t.totalSpent || 'Total Spent',
      sortable: true,
      align: 'right' as const,
      render: (item: Customer) => (
        <div className="flex items-center gap-2 text-white/90">
          <DollarSign className="h-4 w-4 text-white/60" />
          <span>{typeof item?.total_spent === 'number' && item.total_spent > 0 ? formatCurrency(item.total_spent) : '-'}</span>
        </div>
      )
    },
    {
      key: 'created_at',
      label: t.joinDate || 'Join Date',
      sortable: true,
      render: (item: Customer) => (
        <div className="flex items-center gap-2 text-white/90">
          <Calendar className="h-4 w-4 text-white/60" />
          <span className="text-sm">
            {item?.created_at ?
              new Date(item.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US') :
              '-'
            }
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Customer>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Customer) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Customer) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Customer) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { value: 'all', label: t.all || 'All' },
        { value: 'active', label: t.active || 'Active' },
        { value: 'inactive', label: t.inactive || 'Inactive' },
        { value: 'suspended', label: t.suspended || 'Suspended' },
        { value: 'prospect', label: t.prospect || 'Prospect' }
      ]
    },
    {
      key: 'customer_type',
      label: t.customerType,
      options: [
        { value: 'all', label: t.all || 'All' },
        { value: 'individual', label: t.individual || 'Individual' },
        { value: 'business', label: t.business || 'Business' },
        { value: 'enterprise', label: t.enterprise || 'Enterprise' }
      ]
    }
  ]

  // Form fields for modal
  const formFields: FormField[] = [
    {
      name: 'first_name',
      label: t.firstName || 'First Name',
      type: 'text',
      required: true,
      placeholder: 'Enter first name'
    },
    {
      name: 'last_name',
      label: t.lastName || 'Last Name',
      type: 'text',
      placeholder: 'Enter last name'
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true,
      placeholder: 'Enter email address'
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'text',
      placeholder: 'Enter phone number'
    },
    {
      name: 'company_name',
      label: t.company || 'Company',
      type: 'text',
      placeholder: 'Enter company name'
    },
    {
      name: 'company_size',
      label: t.companySize || 'Company Size',
      type: 'text',
      placeholder: 'Enter company size'
    },
    {
      name: 'industry',
      label: t.industry || 'Industry',
      type: 'text',
      placeholder: 'Enter industry'
    },
    {
      name: 'address_line1',
      label: t.address || 'Address',
      type: 'text',
      placeholder: 'Enter address line 1'
    },
    {
      name: 'address_line2',
      label: t.addressLine2 || 'Address Line 2',
      type: 'text',
      placeholder: 'Enter address line 2'
    },
    {
      name: 'city',
      label: t.city || 'City',
      type: 'text',
      placeholder: 'Enter city'
    },
    {
      name: 'state',
      label: t.state || 'State',
      type: 'text',
      placeholder: 'Enter state'
    },
    {
      name: 'postal_code',
      label: t.postalCode || 'Postal Code',
      type: 'text',
      placeholder: 'Enter postal code'
    },
    {
      name: 'country',
      label: t.country || 'Country',
      type: 'text',
      placeholder: 'Enter country'
    },
    {
      name: 'customer_type',
      label: t.customerType,
      type: 'select',
      required: true,
      options: [
        { value: 'individual', label: t.individual || 'Individual' },
        { value: 'business', label: t.business || 'Business' },
        { value: 'enterprise', label: t.enterprise || 'Enterprise' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { value: 'active', label: t.active || 'Active' },
        { value: 'inactive', label: t.inactive || 'Inactive' },
        { value: 'suspended', label: t.suspended || 'Suspended' },
        { value: 'prospect', label: t.prospect || 'Prospect' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Customer>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
      selectItem(null)
    } catch (error) {
      // Error is handled by the CRUD hook
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for customer reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/customer-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `customer-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        const blob = await exportData(format)
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `customers.${format === 'excel' ? 'xlsx' : 'csv'}`
        a.click()
        window.URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.customers}
        data={customers}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCustomer : modalMode === 'edit' ? t.editCustomer : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />


    </div>
  )
}
