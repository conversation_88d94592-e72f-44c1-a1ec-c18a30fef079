/**
 * Projects Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FolderOpen,
  Users,
  Calendar,
  DollarSign,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  User
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { projectService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ProjectsProps {
  language: 'ar' | 'en'
}

interface Project {
  id: string
  name: string
  name_ar?: string
  description?: string
  description_ar?: string
  start_date: string
  end_date: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  budget_amount?: number
  client?: string
  project_manager?: string
  department?: string
  team_members?: any[]
  progress?: number
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    projects: 'المشاريع',
    addProject: 'إضافة مشروع',
    editProject: 'تعديل المشروع',
    searchProjects: 'البحث في المشاريع',
    name: 'اسم المشروع',
    nameAr: 'الاسم بالعربية',
    description: 'الوصف',
    descriptionAr: 'الوصف بالعربية',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    status: 'الحالة',
    priority: 'الأولوية',
    budget: 'الميزانية',
    client: 'العميل',
    projectManager: 'مدير المشروع',
    department: 'القسم',
    teamMembers: 'أعضاء الفريق',
    progress: 'التقدم',
    createdAt: 'تاريخ الإنشاء',
    actions: 'الإجراءات',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    planning: 'التخطيط',
    inProgress: 'قيد التنفيذ',
    onHold: 'معلق',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    critical: 'حرجة',
    searchPlaceholder: 'البحث بالاسم أو الوصف...',
    noProjects: 'لا يوجد مشاريع',
    loading: 'جاري التحميل...',
    manageProjects: 'إدارة المشاريع والمهام',
    createSuccess: 'تم إنشاء المشروع بنجاح',
    updateSuccess: 'تم تحديث المشروع بنجاح',
    deleteSuccess: 'تم حذف المشروع بنجاح',
    confirmDelete: 'هل أنت متأكد من حذف هذا المشروع؟'
  },
  en: {
    projects: 'Projects',
    addProject: 'Add Project',
    editProject: 'Edit Project',
    searchProjects: 'Search Projects',
    name: 'Project Name',
    nameAr: 'Name (Arabic)',
    description: 'Description',
    descriptionAr: 'Description (Arabic)',
    startDate: 'Start Date',
    endDate: 'End Date',
    status: 'Status',
    priority: 'Priority',
    budget: 'Budget',
    client: 'Client',
    projectManager: 'Project Manager',
    department: 'Department',
    teamMembers: 'Team Members',
    progress: 'Progress',
    createdAt: 'Created Date',
    actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    planning: 'Planning',
    inProgress: 'In Progress',
    onHold: 'On Hold',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    searchPlaceholder: 'Search by name or description...',
    noProjects: 'No projects found',
    loading: 'Loading...',
    manageProjects: 'Manage projects and tasks',
    createSuccess: 'Project created successfully',
    updateSuccess: 'Project updated successfully',
    deleteSuccess: 'Project deleted successfully',
    confirmDelete: 'Are you sure you want to delete this project?'
  }
}

export default function Projects({ language }: ProjectsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: projects,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Project>({
    service: projectService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PLANNING':
        return 'bg-gray-500/20 text-gray-300'
      case 'IN_PROGRESS':
        return 'bg-blue-500/20 text-blue-300'
      case 'ON_HOLD':
        return 'bg-yellow-500/20 text-yellow-300'
      case 'COMPLETED':
        return 'bg-green-500/20 text-green-300'
      case 'CANCELLED':
        return 'bg-red-500/20 text-red-300'
      default:
        return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toUpperCase()) {
      case 'LOW':
        return 'text-green-400'
      case 'MEDIUM':
        return 'text-yellow-400'
      case 'HIGH':
        return 'text-orange-400'
      case 'CRITICAL':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: any) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <FolderOpen className="h-5 w-5 text-white" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' && item.name_ar ? item.name_ar : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' && item.description_ar ? item.description_ar : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      align: 'center' as const,
      render: (item: any) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status.toLowerCase().replace('_', '') as keyof typeof t] || item.status}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      align: 'center' as const,
      render: (item: any) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {t[item.priority.toLowerCase() as keyof typeof t] || item.priority}
        </span>
      )
    },
    {
      key: 'project_manager',
      label: t.projectManager,
      render: (item: any) => (
        <div className="flex items-center gap-2 text-white/90">
          <User className="h-4 w-4 text-white/60" />
          <span>{item.project_manager || '-'}</span>
        </div>
      )
    },
    {
      key: 'budget_amount',
      label: t.budget,
      sortable: true,
      align: 'right' as const,
      render: (item: any) => (
        <div className="flex items-center gap-2 text-white/90">
          <DollarSign className="h-4 w-4 text-white/60" />
          <span>{item.budget ? formatCurrency(item.budget) : '-'}</span>
        </div>
      )
    },
    {
      key: 'start_date',
      label: t.startDate,
      sortable: true,
      render: (item: any) => (
        <div className="flex items-center gap-2 text-white/90">
          <Calendar className="h-4 w-4 text-white/60" />
          <span className="text-sm">
            {new Date(item.start_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
          </span>
        </div>
      )
    },
    {
      key: 'end_date',
      label: t.endDate,
      sortable: true,
      render: (item: any) => (
        <div className="flex items-center gap-2 text-white/90">
          <Calendar className="h-4 w-4 text-white/60" />
          <span className="text-sm">
            {new Date(item.end_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { value: 'PLANNING', label: t.planning },
        { value: 'IN_PROGRESS', label: t.inProgress },
        { value: 'ON_HOLD', label: t.onHold },
        { value: 'COMPLETED', label: t.completed },
        { value: 'CANCELLED', label: t.cancelled }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { value: 'LOW', label: t.low },
        { value: 'MEDIUM', label: t.medium },
        { value: 'HIGH', label: t.high },
        { value: 'CRITICAL', label: t.critical }
      ]
    }
  ]

  // Form fields for modal
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true,
      placeholder: 'Enter project name'
    },
    {
      name: 'name_ar',
      label: t.nameAr,
      type: 'text',
      required: true,
      placeholder: 'أدخل اسم المشروع بالعربية'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      placeholder: 'Enter project description',
      rows: 3
    },
    {
      name: 'description_ar',
      label: t.descriptionAr,
      type: 'textarea',
      placeholder: 'أدخل وصف المشروع بالعربية',
      rows: 3
    },
    {
      name: 'client',
      label: t.client,
      type: 'text',
      placeholder: 'Enter client name'
    },
    {
      name: 'project_manager',
      label: t.projectManager,
      type: 'text',
      placeholder: 'Enter project manager name'
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      placeholder: 'Enter department'
    },
    {
      name: 'start_date',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'end_date',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'budget_amount',
      label: t.budget,
      type: 'number',
      placeholder: 'Enter budget amount',
      min: 0
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { value: 'PLANNING', label: t.planning },
        { value: 'IN_PROGRESS', label: t.inProgress },
        { value: 'ON_HOLD', label: t.onHold },
        { value: 'COMPLETED', label: t.completed },
        { value: 'CANCELLED', label: t.cancelled }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { value: 'LOW', label: t.low },
        { value: 'MEDIUM', label: t.medium },
        { value: 'HIGH', label: t.high },
        { value: 'CRITICAL', label: t.critical }
      ]
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Project>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
      selectItem(null)
    } catch (error) {
      // Error is handled by the CRUD hook
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for project reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/project-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `project-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* CRUD Table */}
      <CrudTable
        title={t.projects}
        data={projects}
        columns={columns as any}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProject : modalMode === 'edit' ? t.editProject : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
