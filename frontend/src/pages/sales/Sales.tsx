/**
 * Sales Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  DollarSign,
  Target,
  Users,
  Calendar,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  User,
  Package,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  ShoppingCart
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { salesOrderService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface SalesOrder {
  id: string
  order_number: string
  customer_name: string
  customer_name_ar: string
  amount: number
  date: string
  due_date: string
  sales_person: string
  sales_person_ar: string
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  items_count: number
  discount: number
  tax: number
  total_amount: number
}

interface SalesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    sales: 'المبيعات',
    searchPlaceholder: 'البحث في المبيعات...',
    addSale: 'إضافة مبيعة',
    editSale: 'تعديل المبيعة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المبيعة؟',
    orderNumber: 'رقم الطلب',
    customer: 'العميل',
    amount: 'المبلغ',
    date: 'التاريخ',
    dueDate: 'تاريخ الاستحقاق',
    salesPerson: 'مندوب المبيعات',
    status: 'الحالة',
    priority: 'الأولوية',
    itemsCount: 'عدد العناصر',
    discount: 'الخصم',
    tax: 'الضريبة',
    totalAmount: 'المبلغ الإجمالي',
    notes: 'ملاحظات',
    pending: 'معلق',
    confirmed: 'مؤكد',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    all: 'الكل'
  },
  en: {
    sales: 'Sales',
    searchPlaceholder: 'Search sales...',
    addSale: 'Add Sale',
    editSale: 'Edit Sale',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this sale?',
    orderNumber: 'Order Number',
    customer: 'Customer',
    amount: 'Amount',
    date: 'Date',
    dueDate: 'Due Date',
    salesPerson: 'Sales Person',
    status: 'Status',
    priority: 'Priority',
    itemsCount: 'Items Count',
    discount: 'Discount',
    tax: 'Tax',
    totalAmount: 'Total Amount',
    notes: 'Notes',
    pending: 'Pending',
    confirmed: 'Confirmed',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    all: 'All'
  }
}

export default function Sales({ language }: SalesProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: sales,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SalesOrder>({
    service: salesOrderService,
    autoLoad: true,
    pageSize: 20
  })

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', icon: Clock },
      confirmed: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      shipped: { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30', icon: Package },
      delivered: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      cancelled: { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: XCircle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge className={`${config.color} border`}>
        <Icon className="h-3 w-3 mr-1" />
        {t[status as keyof typeof t] || status}
      </Badge>
    )
  }

  // Priority badge helper
  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30' },
      medium: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' },
      high: { color: 'bg-red-500/20 text-red-400 border-red-500/30' }
    }

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium

    return (
      <Badge className={`${config.color} border`}>
        {t[priority as keyof typeof t] || priority}
      </Badge>
    )
  }

  // Table columns configuration
  const columns: TableColumn<SalesOrder>[] = [
    {
      key: 'order_number',
      label: t.orderNumber,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <ShoppingCart className="h-4 w-4 text-blue-400" />
          <span className="font-medium text-white">{item.order_number}</span>
        </div>
      )
    },
    {
      key: 'customer_name',
      label: t.customer,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-green-400" />
          <span className="text-white">{language === 'ar' ? item.customer_name_ar : item.customer_name}</span>
        </div>
      )
    },
    {
      key: 'total_amount',
      label: t.totalAmount,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-green-400" />
          <span className="font-medium text-white">
            {new Intl.NumberFormat('ar-SA', {
              style: 'currency',
              currency: 'SAR'
            }).format(item.total_amount)}
          </span>
        </div>
      )
    },
    {
      key: 'date',
      label: t.date,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-blue-400" />
          <span className="text-white">{new Date(item.date).toLocaleDateString()}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: SalesOrder) => getStatusBadge(item.status)
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: SalesOrder) => getPriorityBadge(item.priority)
    },
    {
      key: 'sales_person',
      label: t.salesPerson,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-purple-400" />
          <span className="text-white">{language === 'ar' ? item.sales_person_ar : item.sales_person}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.all, value: '' },
        { label: t.pending, value: 'pending' },
        { label: t.confirmed, value: 'confirmed' },
        { label: t.shipped, value: 'shipped' },
        { label: t.delivered, value: 'delivered' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.all, value: '' },
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'order_number',
      label: t.orderNumber,
      type: 'text',
      required: true
    },
    {
      name: 'customer_name',
      label: t.customer,
      type: 'text',
      required: true
    },
    {
      name: 'customer_name_ar',
      label: `${t.customer} (عربي)`,
      type: 'text'
    },
    {
      name: 'amount',
      label: t.amount,
      type: 'number',
      min: 0,
      step: 0.01,
      required: true
    },
    {
      name: 'date',
      label: t.date,
      type: 'date',
      required: true
    },
    {
      name: 'due_date',
      label: t.dueDate,
      type: 'date'
    },
    {
      name: 'sales_person',
      label: t.salesPerson,
      type: 'text',
      required: true
    },
    {
      name: 'sales_person_ar',
      label: `${t.salesPerson} (عربي)`,
      type: 'text'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.confirmed, value: 'confirmed' },
        { label: t.shipped, value: 'shipped' },
        { label: t.delivered, value: 'delivered' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    },
    {
      name: 'items_count',
      label: t.itemsCount,
      type: 'number',
      min: 1
    },
    {
      name: 'discount',
      label: t.discount,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'tax',
      label: t.tax,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'total_amount',
      label: t.totalAmount,
      type: 'number',
      min: 0,
      step: 0.01,
      required: true
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<SalesOrder>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for sales reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/sales-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `sales-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData(format)
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.sales}
        data={sales}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addSale : modalMode === 'edit' ? t.editSale : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
