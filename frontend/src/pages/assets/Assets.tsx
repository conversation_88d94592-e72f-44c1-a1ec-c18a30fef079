/**
 * Assets Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  MapPin,
  // Calendar, // TODO: Add calendar integration for asset schedules
  User,
  // AlertTriangle, // TODO: Add asset status warnings
  CheckCircle,
  // Clock, // TODO: Add time-based asset tracking
  Wrench,
  Eye,
  Edit,
  Trash2,
  DollarSign,
  Building,
  Laptop,
  Car,
  Printer,
  Monitor,
  Smartphone
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { assetService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import { usePermissions, PermissionGate } from '@/components/RoleBasedRoute'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface AssetsProps {
  language: 'ar' | 'en'
}

interface Asset {
  id: number
  assetId: string
  name: string
  nameAr: string
  category: string
  location: string
  assignedTo: string
  status: 'inUse' | 'available' | 'maintenance' | 'retired'
  purchaseDate: string
  purchasePrice: number
  currentValue: number
  warrantyExpiry: string
  serialNumber: string
  manufacturer: string
  model: string
  notes: string
  lastMaintenance: string
  nextMaintenance: string
}

const translations = {
  ar: {
    assets: 'الأصول',
    addAsset: 'إضافة أصل',
    editAsset: 'تعديل الأصل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الأصل؟',
    searchPlaceholder: 'البحث في الأصول...',
    assetId: 'رقم الأصل',
    assetName: 'اسم الأصل',
    category: 'الفئة',
    location: 'الموقع',
    assignedTo: 'مخصص لـ',
    status: 'الحالة',
    purchaseDate: 'تاريخ الشراء',
    purchasePrice: 'سعر الشراء',
    currentValue: 'القيمة الحالية',
    warrantyExpiry: 'انتهاء الضمان',
    serialNumber: 'الرقم التسلسلي',
    manufacturer: 'الشركة المصنعة',
    model: 'الموديل',
    notes: 'ملاحظات',
    lastMaintenance: 'آخر صيانة',
    nextMaintenance: 'الصيانة القادمة',
    available: 'متاح',
    inUse: 'قيد الاستخدام',
    maintenance: 'صيانة',
    retired: 'متقاعد',
    categories: {
      computers: 'أجهزة كمبيوتر',
      furniture: 'أثاث',
      vehicles: 'مركبات',
      equipment: 'معدات',
      electronics: 'إلكترونيات'
    }
  },
  en: {
    assets: 'Assets',
    addAsset: 'Add Asset',
    editAsset: 'Edit Asset',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this asset?',
    searchPlaceholder: 'Search assets...',
    assetId: 'Asset ID',
    assetName: 'Asset Name',
    category: 'Category',
    location: 'Location',
    assignedTo: 'Assigned To',
    status: 'Status',
    purchaseDate: 'Purchase Date',
    purchasePrice: 'Purchase Price',
    currentValue: 'Current Value',
    warrantyExpiry: 'Warranty Expiry',
    serialNumber: 'Serial Number',
    manufacturer: 'Manufacturer',
    model: 'Model',
    notes: 'Notes',
    lastMaintenance: 'Last Maintenance',
    nextMaintenance: 'Next Maintenance',
    available: 'Available',
    inUse: 'In Use',
    maintenance: 'Maintenance',
    retired: 'Retired',
    categories: {
      computers: 'Computers',
      furniture: 'Furniture',
      vehicles: 'Vehicles',
      equipment: 'Equipment',
      electronics: 'Electronics'
    }
  }
}

export default function Assets({ language }: AssetsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Role-based permissions
  const { hasRole, hasPermission, canAccess, userRole } = usePermissions()

  // Define role-based capabilities
  const canCreateAssets = hasRole('SUPERADMIN') || hasRole('ADMIN') || hasRole('HR_MANAGER')
  const canEditAssets = hasRole('SUPERADMIN') || hasRole('ADMIN') || hasRole('HR_MANAGER')
  const canDeleteAssets = hasRole('SUPERADMIN') || hasRole('ADMIN')
  const canViewAllAssets = hasRole('SUPERADMIN') || hasRole('ADMIN') || hasRole('HR_MANAGER') || hasRole('FINANCE_MANAGER')
  const isRegularEmployee = hasRole('EMPLOYEE') || hasRole('INTERN')

  // Use the generic CRUD hook
  const {
    items: assets,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Asset>({
    service: assetService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'computers':
        return <Laptop className="h-4 w-4" />
      case 'furniture':
        return <Building className="h-4 w-4" />
      case 'vehicles':
        return <Car className="h-4 w-4" />
      case 'equipment':
        return <Printer className="h-4 w-4" />
      case 'electronics':
        return <Monitor className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'IN_USE':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'MAINTENANCE':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'RETIRED':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'LOST':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'DAMAGED':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<Asset>[] = [
    {
      key: 'asset_id',
      label: t.assetId,
      sortable: true,
      render: (item: Asset) => (
        <div className="flex items-center gap-2">
          {getCategoryIcon(item.category_name)}
          <span className="font-medium text-white">{item.asset_id}</span>
        </div>
      )
    },
    {
      key: 'name',
      label: t.assetName,
      sortable: true,
      render: (item: Asset) => (
        <div>
          <div className="font-medium text-white">
            {item.name_ar || item.name}
          </div>
          <div className="text-sm text-white/60">
            {item.manufacturer && item.model ? `${item.manufacturer} ${item.model}` :
             item.manufacturer || item.model || 'No model info'}
          </div>
          {item.serial_number && (
            <div className="text-xs text-white/40">S/N: {item.serial_number}</div>
          )}
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Asset) => (
        <Badge variant="outline" className="text-white border-white/20">
          {item.category_name}
        </Badge>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: Asset) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.location}</span>
        </div>
      )
    },
    {
      key: 'assigned_to',
      label: t.assignedTo,
      render: (item: Asset) => (
        item.assigned_to_name ? (
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-green-400" />
            <span className="text-white/80">{item.assigned_to_name}</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Asset) => {
        const statusLabels: Record<string, string> = {
          'AVAILABLE': language === 'ar' ? 'متاح' : 'Available',
          'IN_USE': language === 'ar' ? 'قيد الاستخدام' : 'In Use',
          'MAINTENANCE': language === 'ar' ? 'صيانة' : 'Maintenance',
          'RETIRED': language === 'ar' ? 'متقاعد' : 'Retired',
          'LOST': language === 'ar' ? 'مفقود' : 'Lost',
          'DAMAGED': language === 'ar' ? 'تالف' : 'Damaged'
        }
        return (
          <Badge className={getStatusColor(item.status)}>
            {statusLabels[item.status] || item.status}
          </Badge>
        )
      }
    },
    {
      key: 'purchase_price',
      label: t.purchasePrice,
      sortable: true,
      render: (item: Asset) => (
        <div className="text-sm">
          <div className="text-white font-medium">
            {new Intl.NumberFormat('ar-SA', {
              style: 'currency',
              currency: 'SAR'
            }).format(item.purchase_price)}
          </div>
          {item.current_value && (
            <div className="text-white/60 text-xs">
              Current: {new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
              }).format(item.current_value)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'warranty_expiry',
      label: t.warrantyExpiry,
      sortable: true,
      render: (item: Asset) => (
        <div className="text-sm">
          <div className="text-white/80">{item.warranty_expiry || '-'}</div>
          {item.warranty_expiry && new Date(item.warranty_expiry) < new Date() && (
            <div className="text-red-400 text-xs">منتهي</div>
          )}
        </div>
      )
    }
  ]

  // Table actions configuration with role-based filtering
  const actions: TableAction<Asset>[] = [
    // View action - available to all authenticated users
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Asset) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    // Edit action - only for admins and HR managers
    ...(canEditAssets ? [{
      label: t.edit,
      icon: Edit,
      onClick: (item: Asset) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost' as const
    }] : []),
    // Delete action - only for superadmins and admins
    ...(canDeleteAssets ? [{
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Asset) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost' as const,
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }] : [])
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.computers, value: 'computers' },
        { label: t.categories.furniture, value: 'furniture' },
        { label: t.categories.vehicles, value: 'vehicles' },
        { label: t.categories.equipment, value: 'equipment' },
        { label: t.categories.electronics, value: 'electronics' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.available, value: 'available' },
        { label: t.inUse, value: 'inUse' },
        { label: t.maintenance, value: 'maintenance' },
        { label: t.retired, value: 'retired' }
      ]
    }
  ]

  // FIXED: Form fields configuration with correct backend field mapping
  const formFields: FormField[] = [
    {
      name: 'asset_id', // FIXED: Backend expects 'asset_id'
      label: t.assetId,
      type: 'text',
      required: true,
      placeholder: 'AST-001'
    },
    {
      name: 'name',
      label: t.assetName,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar', // FIXED: Backend expects 'name_ar', not 'nameAr'
      label: t.assetName + ' (عربي)',
      type: 'text',
      required: false // FIXED: Make optional to match backend
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: 'Computers', value: 1 }, // FIXED: Use actual category IDs from database
        { label: 'Electronics', value: 2 },
        { label: 'Office Equipment', value: 3 },
        { label: 'Furniture', value: 4 }
      ]
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      required: true
    },
    {
      name: 'assigned_to', // FIXED: Backend expects 'assigned_to', not 'assignedTo'
      label: t.assignedTo,
      type: 'number' // FIXED: Backend expects employee ID as number
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: 'Available', value: 'AVAILABLE' }, // FIXED: Use exact backend status values
        { label: 'In Use', value: 'IN_USE' },
        { label: 'Maintenance', value: 'MAINTENANCE' },
        { label: 'Retired', value: 'RETIRED' },
        { label: 'Lost', value: 'LOST' },
        { label: 'Damaged', value: 'DAMAGED' }
      ]
    },

    {
      name: 'purchase_date', // FIXED: Backend expects 'purchase_date', not 'purchaseDate'
      label: t.purchaseDate,
      type: 'date',
      required: true
    },
    {
      name: 'purchase_price', // FIXED: Backend expects 'purchase_price'
      label: t.purchasePrice,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'current_value', // FIXED: Backend expects 'current_value', not 'currentValue'
      label: t.currentValue,
      type: 'number',
      min: 0 // FIXED: Add validation
    },
    {
      name: 'warranty_expiry', // FIXED: Backend expects 'warranty_expiry', not 'warrantyExpiry'
      label: t.warrantyExpiry,
      type: 'date'
    },
    {
      name: 'serial_number', // FIXED: Backend expects 'serial_number', not 'serialNumber'
      label: t.serialNumber,
      type: 'text'
    },
    {
      name: 'manufacturer', // FIXED: Backend expects 'manufacturer'
      label: t.manufacturer,
      type: 'text'
    },
    {
      name: 'model',
      label: t.model,
      type: 'text'
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  // FIXED: Handle form submission with proper data transformation
  const handleSave = async (data: Partial<Asset>) => {
    try {
      // FIXED: Transform form data to match backend API expectations exactly
      const transformedData = {
        // Required fields with proper mapping
        asset_id: data.asset_id?.toString().trim(), // FIXED: Use asset_id not asset_tag
        name: data.name?.toString().trim(),
        name_ar: data.name_ar?.toString().trim() || '',
        category: parseInt(data.category?.toString() || '1'), // FIXED: Convert to number (category ID)
        status: data.status?.toString() || 'AVAILABLE', // FIXED: Use correct default status
        location: data.location?.toString().trim(),

        // Date fields - ensure proper format (YYYY-MM-DD)
        purchase_date: data.purchase_date ?
          new Date(data.purchase_date.toString()).toISOString().split('T')[0] : null,
        warranty_expiry: data.warranty_expiry ?
          new Date(data.warranty_expiry.toString()).toISOString().split('T')[0] : null,

        // Numeric fields with proper validation
        purchase_price: parseFloat(data.purchase_price?.toString() || '0') || 0, // FIXED: Use purchase_price
        current_value: parseFloat(data.current_value?.toString() || '0') || null,

        // Optional fields
        assigned_to: data.assigned_to ? parseInt(data.assigned_to.toString()) : null,
        serial_number: data.serial_number?.toString().trim() || '',
        manufacturer: data.manufacturer?.toString().trim() || '', // FIXED: Use manufacturer not brand
        model: data.model?.toString().trim() || '',
        notes: data.notes?.toString().trim() || ''
      }

      console.log('🔧 Asset form data received:', data)
      console.log('🔧 Transformed asset data for API:', transformedData)

      if (modalMode === 'create') {
        await createItem(transformedData as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, transformedData as any)
      }
      setShowModal(false)
    } catch (error) {
      console.error('❌ Asset save error:', error)
      // FIXED: Show error message to user
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      alert(`Error saving asset: ${errorMessage}`)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for asset reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/asset-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `asset-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Role-based information banner */}
      {isRegularEmployee && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                {language === 'ar'
                  ? 'يمكنك عرض الأصول المخصصة لك والأصول المشتركة في قسمك فقط.'
                  : 'You can view assets assigned to you and shared assets in your department only.'
                }
              </p>
            </div>
          </div>
        </div>
      )}

      {/* CRUD Table */}
      <CrudTable
        title={t.assets}
        data={assets}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={canCreateAssets ? handleCreate : undefined}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addAsset : modalMode === 'edit' ? t.editAsset : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
