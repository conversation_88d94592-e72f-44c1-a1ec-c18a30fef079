/**
 * Attendance Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Clock,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Timer,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { attendanceService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface AttendanceProps {
  language: 'ar' | 'en'
}

interface AttendanceRecord {
  id: number
  employee_name: string
  date: string
  check_in: string
  check_out: string
  total_hours: number
  overtime_hours: number
  is_present: boolean
  is_late: boolean
  status: 'present' | 'absent' | 'late' | 'overtime'
  notes?: string
}

const translations = {
  ar: {
    attendance: 'الحضور والانصراف',
    addRecord: 'إضافة سجل حضور',
    editRecord: 'تعديل سجل الحضور',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف سجل الحضور هذا؟',
    searchPlaceholder: 'البحث في سجلات الحضور...',
    employee: 'الموظف',
    date: 'التاريخ',
    checkInTime: 'وقت الحضور',
    checkOutTime: 'وقت الانصراف',
    totalHours: 'إجمالي الساعات',
    overtimeHours: 'ساعات إضافية',
    status: 'الحالة',
    notes: 'ملاحظات',
    present: 'حاضر',
    absent: 'غائب',
    late: 'متأخر',
    overtime: 'وقت إضافي',
    employeeName: 'اسم الموظف',
    isPresent: 'حاضر',
    isLate: 'متأخر'
  },
  en: {
    attendance: 'Attendance',
    addRecord: 'Add Attendance Record',
    editRecord: 'Edit Attendance Record',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this attendance record?',
    searchPlaceholder: 'Search attendance records...',
    employee: 'Employee',
    date: 'Date',
    checkInTime: 'Check In Time',
    checkOutTime: 'Check Out Time',
    totalHours: 'Total Hours',
    overtimeHours: 'Overtime Hours',
    status: 'Status',
    notes: 'Notes',
    present: 'Present',
    absent: 'Absent',
    late: 'Late',
    overtime: 'Overtime',
    employeeName: 'Employee Name',
    isPresent: 'Present',
    isLate: 'Late'
  }
}

export default function Attendance({ language }: AttendanceProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: attendanceRecords,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<AttendanceRecord>({
    service: attendanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-3 w-3" />
      case 'absent':
        return <XCircle className="h-3 w-3" />
      case 'late':
        return <AlertTriangle className="h-3 w-3" />
      case 'overtime':
        return <Timer className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'overtime':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<AttendanceRecord>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: AttendanceRecord) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white">{item.employee_name}</span>
        </div>
      )
    },
    {
      key: 'date',
      label: t.date,
      sortable: true,
      render: (item: AttendanceRecord) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.date}</span>
        </div>
      )
    },
    {
      key: 'check_in',
      label: t.checkInTime,
      render: (item: AttendanceRecord) => (
        <span className="text-white/80">{item.check_in || '-'}</span>
      )
    },
    {
      key: 'check_out',
      label: t.checkOutTime,
      render: (item: AttendanceRecord) => (
        <span className="text-white/80">{item.check_out || '-'}</span>
      )
    },
    {
      key: 'total_hours',
      label: t.totalHours,
      sortable: true,
      render: (item: AttendanceRecord) => (
        <span className="text-white font-medium">
          {item.total_hours ? `${item.total_hours}h` : '-'}
        </span>
      )
    },
    {
      key: 'overtime_hours',
      label: t.overtimeHours,
      render: (item: AttendanceRecord) => (
        <span className="text-white/80">
          {item.overtime_hours ? `${item.overtime_hours}h` : '0h'}
        </span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: AttendanceRecord) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{t[item.status as keyof typeof t]}</span>
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<AttendanceRecord>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: AttendanceRecord) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: AttendanceRecord) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: AttendanceRecord) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.present, value: 'present' },
        { label: t.absent, value: 'absent' },
        { label: t.late, value: 'late' },
        { label: t.overtime, value: 'overtime' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee_name',
      label: t.employeeName,
      type: 'text',
      required: true
    },
    {
      name: 'date',
      label: t.date,
      type: 'date',
      required: true
    },
    {
      name: 'check_in',
      label: t.checkInTime,
      type: 'text'
    },
    {
      name: 'check_out',
      label: t.checkOutTime,
      type: 'text'
    },
    {
      name: 'total_hours',
      label: t.totalHours,
      type: 'number',
      min: 0,
      step: 0.5
    },
    {
      name: 'overtime_hours',
      label: t.overtimeHours,
      type: 'number',
      min: 0,
      step: 0.5
    },
    {
      name: 'is_present',
      label: t.isPresent,
      type: 'checkbox'
    },
    {
      name: 'is_late',
      label: t.isLate,
      type: 'checkbox'
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<AttendanceRecord>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for attendance reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/attendance-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `attendance-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        const blob = await exportData(format)
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `attendance.${format === 'excel' ? 'xlsx' : 'csv'}`
        a.click()
        window.URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.attendance}
        data={attendanceRecords}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addRecord : modalMode === 'edit' ? t.editRecord : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
