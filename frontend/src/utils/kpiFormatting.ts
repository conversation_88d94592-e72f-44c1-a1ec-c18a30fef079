/**
 * KPI Formatting Utilities
 * Provides consistent formatting for KPI values, targets, and percentages
 */

export interface KPIFormatOptions {
  language?: 'ar' | 'en'
  showUnit?: boolean
  precision?: number
  compact?: boolean
}

/**
 * Format KPI value based on unit and type
 */
export function formatKPIValue(
  value: number | null | undefined, 
  unit: string = '', 
  options: KPIFormatOptions = {}
): string {
  const { language = 'en', showUnit = true, precision = 2, compact = false } = options

  // Handle null/undefined values
  if (value === null || value === undefined || typeof value !== 'number' || isNaN(value)) {
    return '--'
  }

  // Determine formatting based on unit
  switch (unit.toLowerCase()) {
    case '%':
    case 'percent':
    case 'percentage':
      return formatPercentage(value, precision)
    
    case 'sar':
    case 'riyal':
    case 'riyals':
      return formatCurrency(value, 'SAR', language, compact)
    
    case 'usd':
    case '$':
    case 'dollar':
    case 'dollars':
      return formatCurrency(value, 'USD', language, compact)
    
    case 'hours':
    case 'hour':
    case 'ساعة':
    case 'ساعات':
      return formatHours(value, language, precision)
    
    case 'days':
    case 'day':
    case 'يوم':
    case 'أيام':
      return formatDays(value, language, precision)
    
    case '/5':
    case 'out of 5':
    case 'من 5':
      return formatRating(value, 5, precision)
    
    case '/10':
    case 'out of 10':
    case 'من 10':
      return formatRating(value, 10, precision)
    
    default:
      // Generic number formatting
      return formatNumber(value, unit, language, precision, showUnit, compact)
  }
}

/**
 * Format percentage values
 */
function formatPercentage(value: number, precision: number = 1): string {
  return `${value.toFixed(precision)}%`
}

/**
 * Format currency values
 */
function formatCurrency(
  value: number, 
  currency: string = 'SAR', 
  language: string = 'en',
  compact: boolean = false
): string {
  const locale = language === 'ar' ? 'ar-SA' : 'en-US'
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: compact ? 0 : 2,
      maximumFractionDigits: compact ? 0 : 2,
      notation: compact && value >= 1000 ? 'compact' : 'standard'
    }).format(value)
  } catch (error) {
    // Fallback formatting
    const symbol = currency === 'SAR' ? 'ر.س' : '$'
    const formattedValue = compact ? formatCompactNumber(value) : value.toLocaleString()
    return language === 'ar' ? `${formattedValue} ${symbol}` : `${symbol}${formattedValue}`
  }
}

/**
 * Format hours
 */
function formatHours(value: number, language: string = 'en', precision: number = 1): string {
  const formattedValue = value.toFixed(precision)
  const unit = language === 'ar' ? 
    (value === 1 ? 'ساعة' : 'ساعات') : 
    (value === 1 ? 'hour' : 'hours')
  
  return `${formattedValue} ${unit}`
}

/**
 * Format days
 */
function formatDays(value: number, language: string = 'en', precision: number = 0): string {
  const formattedValue = value.toFixed(precision)
  const unit = language === 'ar' ? 
    (value === 1 ? 'يوم' : 'أيام') : 
    (value === 1 ? 'day' : 'days')
  
  return `${formattedValue} ${unit}`
}

/**
 * Format rating values (e.g., 4.5/5)
 */
function formatRating(value: number, maxRating: number = 5, precision: number = 1): string {
  return `${value.toFixed(precision)}/${maxRating}`
}

/**
 * Format generic numbers
 */
function formatNumber(
  value: number, 
  unit: string = '', 
  language: string = 'en',
  precision: number = 2,
  showUnit: boolean = true,
  compact: boolean = false
): string {
  let formattedValue: string

  if (compact && value >= 1000) {
    formattedValue = formatCompactNumber(value)
  } else if (precision === 0) {
    formattedValue = Math.round(value).toLocaleString()
  } else {
    formattedValue = value.toFixed(precision)
  }

  if (showUnit && unit) {
    return language === 'ar' ? `${formattedValue} ${unit}` : `${formattedValue} ${unit}`
  }

  return formattedValue
}

/**
 * Format large numbers in compact form (1K, 1M, etc.)
 */
function formatCompactNumber(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toString()
}

/**
 * Format target display with proper context
 */
export function formatTargetDisplay(
  targetValue: number | null | undefined,
  unit: string = '',
  language: string = 'en'
): string {
  // Handle various falsy values more carefully
  if (targetValue === null || targetValue === undefined || targetValue === 0) {
    return language === 'ar' ? 'لا يوجد هدف' : 'No target'
  }

  // Convert string numbers to actual numbers
  const numericTarget = typeof targetValue === 'string' ? parseFloat(targetValue) : targetValue

  if (isNaN(numericTarget)) {
    return language === 'ar' ? 'لا يوجد هدف' : 'No target'
  }

  const formattedTarget = formatKPIValue(numericTarget, unit, { language, precision: 1 })
  const targetLabel = language === 'ar' ? 'الهدف:' : 'Target:'

  return `${targetLabel} ${formattedTarget}`
}

/**
 * Calculate and format achievement percentage
 */
export function formatAchievementPercentage(
  currentValue: number | null | undefined,
  targetValue: number | null | undefined,
  language: string = 'en'
): string {
  if (!currentValue || !targetValue || targetValue === 0) {
    return '--'
  }

  const percentage = Math.round((currentValue / targetValue) * 100)
  return `${percentage}%`
}

/**
 * Get status color based on achievement
 */
export function getKPIStatusColor(
  currentValue: number | null | undefined,
  targetValue: number | null | undefined,
  isHigherBetter: boolean = true
): string {
  if (!currentValue || !targetValue) {
    return 'text-gray-400'
  }

  const achievement = (currentValue / targetValue) * 100

  if (isHigherBetter) {
    if (achievement >= 100) return 'text-green-400'
    if (achievement >= 80) return 'text-yellow-400'
    return 'text-red-400'
  } else {
    // For KPIs where lower is better (e.g., turnover rate)
    if (achievement <= 100) return 'text-green-400'
    if (achievement <= 120) return 'text-yellow-400'
    return 'text-red-400'
  }
}

/**
 * Get trend icon based on direction
 */
export function getTrendIcon(
  trend: string | { direction: string } | null | undefined,
  isHigherBetter: boolean = true
): string {
  let direction = 'stable'
  
  if (typeof trend === 'string') {
    direction = trend
  } else if (trend && typeof trend === 'object' && trend.direction) {
    direction = trend.direction
  }

  switch (direction) {
    case 'up':
      return isHigherBetter ? '↗️' : '↘️'
    case 'down':
      return isHigherBetter ? '↘️' : '↗️'
    default:
      return '➡️'
  }
}

/**
 * Validate and normalize KPI unit
 */
export function normalizeKPIUnit(unit: string | null | undefined): string {
  if (!unit) return ''
  
  const unitMap: { [key: string]: string } = {
    'percent': '%',
    'percentage': '%',
    'sar': 'SAR',
    'riyal': 'SAR',
    'riyals': 'SAR',
    'usd': 'USD',
    'dollar': 'USD',
    'dollars': 'USD',
    'hour': 'hours',
    'day': 'days',
    'ساعة': 'ساعات',
    'يوم': 'أيام'
  }

  const normalizedUnit = unit.toLowerCase().trim()
  return unitMap[normalizedUnit] || unit
}
