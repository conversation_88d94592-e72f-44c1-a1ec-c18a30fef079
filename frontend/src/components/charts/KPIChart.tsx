/**
 * KPI Chart Component
 * Specialized chart component for KPI data visualization with drill-down capabilities
 */

import React, { useState, useEffect, useMemo } from 'react'
import InteractiveChart, { ChartDataPoint, DrillDownLevel } from './InteractiveChart'
import { KPI, KPIValue } from '@/services/kpiService'
import kpiService from '@/services/kpiService'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Calendar,
  Filter,
  Download,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Target,
  Activity
} from 'lucide-react'

export interface KPIChartProps {
  kpi: KPI
  language: 'ar' | 'en'
  type?: 'line' | 'area' | 'bar' | 'composed'
  height?: number
  timeRange?: 'week' | 'month' | 'quarter' | 'year'
  showTarget?: boolean
  showTrend?: boolean
  enableDrillDown?: boolean
  enableExport?: boolean
  onDataPointClick?: (dataPoint: ChartDataPoint, kpi: KPI) => void
}

const translations = {
  ar: {
    loading: 'جاري التحميل...',
    error: 'خطأ في تحميل البيانات',
    retry: 'إعادة المحاولة',
    noData: 'لا توجد بيانات',
    week: 'أسبوع',
    month: 'شهر',
    quarter: 'ربع سنة',
    year: 'سنة',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    performance: 'الأداء',
    trend: 'الاتجاه',
    target: 'الهدف',
    actual: 'الفعلي',
    achievement: 'الإنجاز',
    lastUpdated: 'آخر تحديث'
  },
  en: {
    loading: 'Loading...',
    error: 'Error loading data',
    retry: 'Retry',
    noData: 'No data available',
    week: 'Week',
    month: 'Month',
    quarter: 'Quarter',
    year: 'Year',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    performance: 'Performance',
    trend: 'Trend',
    target: 'Target',
    actual: 'Actual',
    achievement: 'Achievement',
    lastUpdated: 'Last Updated'
  }
}

export default function KPIChart({
  kpi,
  language,
  type = 'line',
  height = 400,
  timeRange = 'month',
  showTarget = true,
  showTrend = true,
  enableDrillDown = true,
  enableExport = true,
  onDataPointClick
}: KPIChartProps) {
  const [values, setValues] = useState<KPIValue[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange)
  
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load KPI values
  useEffect(() => {
    const loadKPIValues = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const endDate = new Date()
        const startDate = new Date()
        
        // Calculate start date based on time range
        switch (selectedTimeRange) {
          case 'week':
            startDate.setDate(endDate.getDate() - 7)
            break
          case 'month':
            startDate.setMonth(endDate.getMonth() - 1)
            break
          case 'quarter':
            startDate.setMonth(endDate.getMonth() - 3)
            break
          case 'year':
            startDate.setFullYear(endDate.getFullYear() - 1)
            break
        }
        
        const kpiValues = await kpiService.getKPIValues(
          kpi.id,
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0]
        )
        
        setValues(kpiValues)
      } catch (err) {
        console.error('Error loading KPI values:', err)
        setError(t.error)
      } finally {
        setLoading(false)
      }
    }

    loadKPIValues()
  }, [kpi.id, selectedTimeRange, t.error])

  // Transform KPI values to chart data
  const chartData = useMemo((): ChartDataPoint[] => {
    if (!values.length) return []
    
    return values.map((value, index) => ({
      name: formatDate(value.date, selectedTimeRange),
      value: value.value,
      target: kpi.target_value || undefined,
      date: value.date,
      achievement: kpi.target_value ? (value.value / kpi.target_value) * 100 : undefined,
      category: kpi.category_name,
      kpiId: kpi.id,
      kpiName: language === 'ar' ? kpi.name_ar || kpi.name : kpi.name,
      unit: kpi.unit,
      notes: value.notes,
      recordedBy: value.recorded_by_name,
      recordedAt: value.recorded_at
    }))
  }, [values, kpi, selectedTimeRange, language])

  // Create drill-down levels
  const drillDownLevels = useMemo((): DrillDownLevel[] => {
    if (!enableDrillDown || !values.length) return []
    
    const levels: DrillDownLevel[] = []
    
    // Level 0: Current time range
    levels.push({
      level: 0,
      title: `${kpi.name} - ${t[selectedTimeRange]}`,
      data: chartData,
      breadcrumb: [kpi.name, t[selectedTimeRange]]
    })
    
    // Level 1: Daily breakdown (if not already daily)
    if (selectedTimeRange !== 'week') {
      const dailyData = generateDailyBreakdown(chartData)
      levels.push({
        level: 1,
        title: `${kpi.name} - ${t.daily}`,
        data: dailyData,
        breadcrumb: [kpi.name, t[selectedTimeRange], t.daily]
      })
    }
    
    return levels
  }, [enableDrillDown, values, chartData, kpi.name, selectedTimeRange, t])

  // Format date based on time range
  const formatDate = (dateString: string, range: string) => {
    const date = new Date(dateString)
    
    switch (range) {
      case 'week':
        return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        })
      case 'month':
        return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', { 
          month: 'short', 
          day: 'numeric' 
        })
      case 'quarter':
      case 'year':
        return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', { 
          month: 'short', 
          year: 'numeric' 
        })
      default:
        return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
    }
  }

  // Generate daily breakdown for drill-down
  const generateDailyBreakdown = (data: ChartDataPoint[]): ChartDataPoint[] => {
    // This would typically come from the API with more granular data
    // For now, we'll return the original data since we don't have daily breakdown API
    // TODO: Implement API endpoint for daily KPI breakdown
    return data
  }

  // Value formatter
  const valueFormatter = (value: number) => {
    if (kpi.measurement_type === 'CURRENCY') {
      return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(value)
    } else if (kpi.measurement_type === 'PERCENTAGE') {
      return `${typeof value === 'number' ? value.toFixed(1) : '0.0'}%`
    } else {
      return `${typeof value === 'number' ? value.toLocaleString() : '0'} ${kpi.unit || ''}`
    }
  }

  // Handle data point click
  const handleDataPointClick = (dataPoint: ChartDataPoint) => {
    if (onDataPointClick) {
      onDataPointClick(dataPoint, kpi)
    }
  }

  // Handle drill-down
  const handleDrillDown = (dataPoint: ChartDataPoint, level: number) => {
    console.log('Drilling down to level:', level, 'for data point:', dataPoint)
    // Additional drill-down logic can be implemented here
  }

  // Retry loading data
  const handleRetry = () => {
    setError(null)
    setLoading(true)
    // Trigger useEffect by changing a dependency
    setSelectedTimeRange(prev => prev)
  }

  if (loading) {
    return (
      <Card className="glass-card border-white/20">
        <CardHeader>
          <Skeleton className="h-6 w-48 bg-white/10" />
          <Skeleton className="h-4 w-32 bg-white/10" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full bg-white/10" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{kpi.name}</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center h-64">
          <p className="text-red-400 mb-4">{error}</p>
          <Button onClick={handleRetry} className="glass-button">
            <RefreshCw className="h-4 w-4 mr-2" />
            {t.retry}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-white/70" />
          <div className="flex items-center gap-1">
            {(['week', 'month', 'quarter', 'year'] as const).map((range) => (
              <Button
                key={range}
                size="sm"
                variant={selectedTimeRange === range ? 'default' : 'ghost'}
                onClick={() => setSelectedTimeRange(range)}
                className={`text-xs ${
                  selectedTimeRange === range 
                    ? 'bg-white/20 text-white' 
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                {t[range]}
              </Button>
            ))}
          </div>
        </div>
        
        {/* KPI Status */}
        <div className="flex items-center gap-2">
          <Badge 
            variant="outline" 
            className={`
              ${kpi.target_achievement && kpi.target_achievement >= 100 
                ? 'text-green-400 border-green-400' 
                : kpi.target_achievement && kpi.target_achievement >= 80
                ? 'text-yellow-400 border-yellow-400'
                : 'text-red-400 border-red-400'}
            `}
          >
            {kpi.target_achievement ? `${kpi.target_achievement.toFixed(1)}%` : 'N/A'}
          </Badge>
          {kpi.trend && (
            <Badge variant="outline" className="text-white/70 border-white/30">
              {kpi.trend.direction === 'up' ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-400" />
              ) : kpi.trend.direction === 'down' ? (
                <TrendingDown className="h-3 w-3 mr-1 text-red-400" />
              ) : (
                <Activity className="h-3 w-3 mr-1 text-gray-400" />
              )}
              {typeof kpi.trend.change_percentage === 'number' ? kpi.trend.change_percentage.toFixed(1) : '0.0'}%
            </Badge>
          )}
        </div>
      </div>

      {/* Interactive Chart */}
      <InteractiveChart
        data={chartData}
        type={type}
        title={language === 'ar' ? kpi.name_ar || kpi.name : kpi.name}
        subtitle={`${t.performance} - ${t[selectedTimeRange]}`}
        height={height}
        language={language}
        enableDrillDown={enableDrillDown}
        enableZoom={true}
        enableBrush={chartData.length > 10}
        enableTooltips={true}
        enableLegend={showTarget}
        enableExport={enableExport}
        drillDownLevels={drillDownLevels}
        onDrillDown={handleDrillDown}
        onDataPointClick={handleDataPointClick}
        colors={['#3B82F6', '#F59E0B', '#10B981']}
        showTarget={showTarget}
        showTrend={showTrend}
        valueFormatter={valueFormatter}
      />
    </div>
  )
}
