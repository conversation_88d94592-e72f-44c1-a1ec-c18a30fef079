/**
 * KPI Dashboard Chart Component
 * Chart component for displaying multiple KPIs in a dashboard
 */

import React, { useState, useEffect, useMemo } from 'react'
import InteractiveChart, { ChartDataPoint } from './InteractiveChart'
import { KPI, KPICategory, useKPIModals } from '@/services/kpiService'
import kpiService from '@/services/kpiService'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Calendar,
  Filter,
  Download,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Target,
  Activity,
  Eye,
  BarChart3,
  PieChart,
  LineChart as LineChartIcon,
  AreaChart
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
// Modal functionality removed - using Redux instead

export interface KPIDashboardChartProps {
  title: string
  subtitle?: string
  kpis: KPI[]
  categories?: KPICategory[]
  language: 'ar' | 'en'
  type?: 'line' | 'area' | 'bar' | 'pie' | 'composed'
  height?: number
  timeRange?: 'week' | 'month' | 'quarter' | 'year'
  showTarget?: boolean
  showTrend?: boolean
  enableDrillDown?: boolean
  enableExport?: boolean
  onViewDetails?: () => void
}

const translations = {
  ar: {
    loading: 'جاري التحميل...',
    error: 'خطأ في تحميل البيانات',
    retry: 'إعادة المحاولة',
    noData: 'لا توجد بيانات',
    week: 'أسبوع',
    month: 'شهر',
    quarter: 'ربع سنة',
    year: 'سنة',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    performance: 'الأداء',
    trend: 'الاتجاه',
    target: 'الهدف',
    actual: 'الفعلي',
    achievement: 'الإنجاز',
    lastUpdated: 'آخر تحديث',
    viewDetails: 'عرض التفاصيل',
    viewKPI: 'عرض المؤشر',
    chartType: 'نوع الرسم البياني',
    line: 'خط',
    area: 'مساحة',
    bar: 'شريط',
    pie: 'دائري',
    composed: 'مركب'
  },
  en: {
    loading: 'Loading...',
    error: 'Error loading data',
    retry: 'Retry',
    noData: 'No data available',
    week: 'Week',
    month: 'Month',
    quarter: 'Quarter',
    year: 'Year',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    performance: 'Performance',
    trend: 'Trend',
    target: 'Target',
    actual: 'Actual',
    achievement: 'Achievement',
    lastUpdated: 'Last Updated',
    viewDetails: 'View Details',
    viewKPI: 'View KPI',
    chartType: 'Chart Type',
    line: 'Line',
    area: 'Area',
    bar: 'Bar',
    pie: 'Pie',
    composed: 'Composed'
  }
}

export default function KPIDashboardChart({
  title,
  subtitle,
  kpis,
  categories = [],
  language,
  type: initialType = 'line',
  height = 400,
  timeRange: initialTimeRange = 'month',
  showTarget = true,
  showTrend = true,
  enableDrillDown = true,
  enableExport = true,
  onViewDetails
}: KPIDashboardChartProps) {
  const [chartData, setChartData] = useState<ChartDataPoint[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState(initialTimeRange)
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar' | 'pie' | 'composed'>(initialType)
  
  const kpiModals = useKPIModals()
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load KPI data
  useEffect(() => {
    const loadKPIData = async () => {
      if (!kpis.length) {
        setChartData([])
        setLoading(false)
        return
      }
      
      setLoading(true)
      setError(null)
      
      try {
        const endDate = new Date()
        const startDate = new Date()
        
        // Calculate start date based on time range
        switch (selectedTimeRange) {
          case 'week':
            startDate.setDate(endDate.getDate() - 7)
            break
          case 'month':
            startDate.setMonth(endDate.getMonth() - 1)
            break
          case 'quarter':
            startDate.setMonth(endDate.getMonth() - 3)
            break
          case 'year':
            startDate.setFullYear(endDate.getFullYear() - 1)
            break
        }
        
        // Prepare data for chart
        const data: ChartDataPoint[] = []
        
        // For pie chart, use current values
        if (chartType === 'pie') {
          kpis.forEach((kpi, index) => {
            const categoryObj = categories.find(c => c.id.toString() === kpi.category)
            
            data.push({
              name: language === 'ar' ? kpi.name_ar || kpi.name : kpi.name,
              value: kpi.current_value?.value || 0,
              target: kpi.target_value || undefined,
              category: kpi.category_name,
              kpiId: kpi.id,
              color: categoryObj?.color || getDefaultColor(index),
              achievement: kpi.target_achievement
            })
          })
        } 
        // For other chart types, get time series data
        else {
          // Get time periods based on selected range
          const periods = generateTimePeriods(startDate, endDate, selectedTimeRange)
          
          // Create data points for each period
          periods.forEach(period => {
            const dataPoint: ChartDataPoint = {
              name: formatDate(period, selectedTimeRange, language),
              date: period,
              value: 0
            }
            
            // Add a property for each KPI
            kpis.forEach((kpi, index) => {
              dataPoint[`kpi${index}`] = 0
              dataPoint[`kpi${index}Name`] = language === 'ar' ? kpi.name_ar || kpi.name : kpi.name
              dataPoint[`kpi${index}Target`] = kpi.target_value || undefined
            })
            
            data.push(dataPoint)
          })
          
          // Fetch values for each KPI and update data points
          for (const kpi of kpis) {
            try {
              const values = await kpiService.getKPIValues(kpi.id, {
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
              })
              
              // Update data points with actual values
              values.forEach(value => {
                const periodStart = value.period_start
                const dataPoint = data.find(d => d.date === periodStart)
                
                if (dataPoint) {
                  const kpiIndex = kpis.findIndex(k => k.id === kpi.id)
                  if (kpiIndex !== -1) {
                    dataPoint[`kpi${kpiIndex}`] = value.value
                  }
                }
              })
            } catch (err) {
              console.error(`Error loading values for KPI ${kpi.id}:`, err)
            }
          }
        }
        
        setChartData(data)
      } catch (err) {
        console.error('Error loading KPI data:', err)
        setError(t.error)
      } finally {
        setLoading(false)
      }
    }

    loadKPIData()
  }, [kpis, categories, selectedTimeRange, chartType, language, t.error])

  // Generate time periods based on selected range
  const generateTimePeriods = (startDate: Date, endDate: Date, range: string): string[] => {
    const periods: string[] = []
    const currentDate = new Date(startDate)
    
    while (currentDate <= endDate) {
      periods.push(currentDate.toISOString().split('T')[0])
      
      switch (range) {
        case 'week':
          currentDate.setDate(currentDate.getDate() + 1) // Daily
          break
        case 'month':
          currentDate.setDate(currentDate.getDate() + 7) // Weekly
          break
        case 'quarter':
          currentDate.setDate(currentDate.getDate() + 14) // Bi-weekly
          break
        case 'year':
          currentDate.setMonth(currentDate.getMonth() + 1) // Monthly
          break
      }
    }
    
    return periods
  }

  // Format date based on time range
  const formatDate = (dateString: string, range: string, lang: string) => {
    const date = new Date(dateString)
    
    switch (range) {
      case 'week':
        return date.toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        })
      case 'month':
        return date.toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US', { 
          month: 'short', 
          day: 'numeric' 
        })
      case 'quarter':
      case 'year':
        return date.toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US', { 
          month: 'short', 
          year: 'numeric' 
        })
      default:
        return date.toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US')
    }
  }

  // Get default color for KPI
  const getDefaultColor = (index: number) => {
    const colors = [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
      '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
    ]
    return colors[index % colors.length]
  }

  // Value formatter
  const valueFormatter = (value: number) => {
    return value.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')
  }

  // Handle data point click
  const handleDataPointClick = (dataPoint: ChartDataPoint) => {
    // If it's a pie chart, the dataPoint contains the KPI directly
    if (chartType === 'pie' && dataPoint.kpiId) {
      const kpi = kpis.find(k => k.id === dataPoint.kpiId)
      if (kpi) {
        kpiModals.openViewKPIModal(kpi, categories)
      }
    } 
    // For other charts, check if the click is on a specific KPI line
    else {
      for (let i = 0; i < kpis.length; i++) {
        if (dataPoint[`kpi${i}`] !== undefined) {
          kpiModals.openViewKPIModal(kpis[i], categories)
          break
        }
      }
    }
  }

  // Retry loading data
  const handleRetry = () => {
    setError(null)
    setLoading(true)
    // Trigger useEffect by changing a dependency
    setSelectedTimeRange(prev => prev)
  }

  // Get chart type icon
  const getChartTypeIcon = (type: string) => {
    switch (type) {
      case 'line':
        return <LineChartIcon className="h-4 w-4" />
      case 'area':
        return <AreaChart className="h-4 w-4" />
      case 'bar':
        return <BarChart3 className="h-4 w-4" />
      case 'pie':
        return <PieChart className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <Card className="glass-card border-white/20">
        <CardHeader>
          <Skeleton className="h-6 w-48 bg-white/10" />
          <Skeleton className="h-4 w-32 bg-white/10" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full bg-white/10" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{title}</CardTitle>
          {subtitle && <CardDescription className="text-white/70">{subtitle}</CardDescription>}
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center h-64">
          <p className="text-red-400 mb-4">{error}</p>
          <Button onClick={handleRetry} className="glass-button">
            <RefreshCw className="h-4 w-4 mr-2" />
            {t.retry}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-white">{title}</CardTitle>
            {subtitle && <CardDescription className="text-white/70">{subtitle}</CardDescription>}
          </div>
          
          <div className="flex items-center gap-2">
            {/* Chart Type Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                  {getChartTypeIcon(chartType)}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="glass-card border-white/20">
                <DropdownMenuItem 
                  onClick={() => setChartType('line')}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  {t.line}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setChartType('area')}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <AreaChart className="h-4 w-4 mr-2" />
                  {t.area}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setChartType('bar')}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  {t.bar}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setChartType('pie')}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <PieChart className="h-4 w-4 mr-2" />
                  {t.pie}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* View Details Button */}
            {onViewDetails && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onViewDetails}
                className="text-white/70 hover:text-white"
              >
                <Eye className="h-4 w-4 mr-2" />
                {t.viewDetails}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Time Range Selector (not for pie charts) */}
          {chartType !== 'pie' && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-white/70" />
              <div className="flex items-center gap-1">
                {(['week', 'month', 'quarter', 'year'] as const).map((range) => (
                  <Button
                    key={range}
                    size="sm"
                    variant={selectedTimeRange === range ? 'default' : 'ghost'}
                    onClick={() => setSelectedTimeRange(range)}
                    className={`text-xs ${
                      selectedTimeRange === range 
                        ? 'bg-white/20 text-white' 
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    {t[range]}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Interactive Chart */}
          <InteractiveChart
            data={chartData}
            type={chartType}
            title=""
            height={height}
            language={language}
            enableDrillDown={enableDrillDown}
            enableZoom={chartType !== 'pie'}
            enableBrush={chartType !== 'pie' && chartData.length > 10}
            enableTooltips={true}
            enableLegend={true}
            enableExport={enableExport}
            onDataPointClick={handleDataPointClick}
            colors={kpis.map((_, i) => getDefaultColor(i))}
            showTarget={showTarget && chartType !== 'pie'}
            showTrend={showTrend && chartType !== 'pie'}
            valueFormatter={valueFormatter}
          />
        </div>
      </CardContent>
    </Card>
  )
}
