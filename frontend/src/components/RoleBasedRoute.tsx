import { ReactNode } from 'react'
import { useSelector } from 'react-redux'
import { Navigate } from 'react-router-dom'
import type { RootState } from '../store'

interface RoleBasedRouteProps {
  children: ReactNode
  requiredPermissions?: string[]
  requiredRole?: string
  minRoleLevel?: number
  fallbackPath?: string
}

export default function RoleBasedRoute({
  children,
  requiredPermissions = [],
  requiredRole,
  minRoleLevel,
  fallbackPath = '/unauthorized'
}: RoleBasedRouteProps) {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth)

  // TEMPORARY FIX: Check localStorage tokens if Redux state is not synchronized
  const hasTokens = localStorage.getItem('access_token') && localStorage.getItem('refresh_token')

  // If not authenticated and no tokens, redirect to login
  if (!isAuthenticated && !hasTokens) {
    return <Navigate to="/login" replace />
  }

  // If no user but has tokens, allow access (authentication state sync issue)
  if (!user && hasTokens) {
    console.warn('RoleBasedRoute: User not in Redux state but tokens present - allowing access')
    return <>{children}</>
  }

  // If not authenticated but has user, redirect to login
  if (!isAuthenticated && !user) {
    return <Navigate to="/login" replace />
  }

  // Ensure user has a role before proceeding
  if (!user.role || !user.role.id) {
    console.error('RoleBasedRoute: User has no role assigned')
    return <Navigate to={fallbackPath} replace />
  }

  // Check role-based access - allow super_admin to access all routes
  if (requiredRole) {
    // FIXED: Use backend role name instead of role.id
    const userRoleName = user.role.name
    const isSuperAdmin = userRoleName === 'SUPERADMIN'

    // Map frontend required role to backend role name
    const roleMapping = {
      'super_admin': 'SUPERADMIN',
      'superadmin': 'SUPERADMIN',
      'admin': 'ADMIN',
      'hr_manager': 'HR_MANAGER',
      'finance_manager': 'FINANCE_MANAGER',
      'department_manager': 'DEPARTMENT_MANAGER',
      'sales_manager': 'SALES_MANAGER',
      'employee': 'EMPLOYEE',
      'user': 'USER'
    }

    const requiredBackendRole = roleMapping[requiredRole.toLowerCase() as keyof typeof roleMapping] || requiredRole.toUpperCase()
    const hasRequiredRole = userRoleName === requiredBackendRole

    // Super admin and admin can access all routes, otherwise check specific role
    const isAdmin = userRoleName === 'ADMIN'
    if (!isSuperAdmin && !isAdmin && !hasRequiredRole) {
      console.warn(`Access denied: User role '${userRoleName}' does not match required role '${requiredBackendRole}'`)
      return <Navigate to={fallbackPath} replace />
    }
  }

  // Check minimum role level - lower numbers = higher privileges
  if (minRoleLevel && user.role && 'level' in user.role) {
    const roleLevel = (user.role as any).level
    if (typeof roleLevel === 'number' && roleLevel > minRoleLevel) {
      console.warn(`Access denied: User role level '${roleLevel}' is higher than required level '${minRoleLevel}'`)
      return <Navigate to={fallbackPath} replace />
    }
  }

  // Check specific permissions
  const userPermissions = user.role?.permissions || []
  if (requiredPermissions.length > 0 && userPermissions.length > 0) {
    const hasPermission = requiredPermissions.every((permission: string) => {
      // Convert permissions to any to handle different formats
      const permissions = userPermissions as any[]

      // Check for wildcard permissions
      if (permissions.some((p: any) =>
        (typeof p === 'string' && p === '*') ||
        (typeof p === 'object' && p.module === '*' && p.actions?.includes('*'))
      )) {
        return true
      }

      // Check specific permissions
      if (typeof permissions[0] === 'string') {
        // String array format
        return permissions.includes(permission) || permissions.includes('*')
      } else {
        // Object array format
        const [module, action] = permission.split('.')
        return permissions.some((p: any) => {
          if (p.module === module || p.module === '*') {
            return p.actions?.includes(action) || p.actions?.includes('*')
          }
          return false
        })
      }
    })

    if (!hasPermission) {
      return <Navigate to={fallbackPath} replace />
    }
  }

  return <>{children}</>
}

// Hook for checking permissions in components
export function usePermissions() {
  const { user } = useSelector((state: RootState) => state.auth)

  const hasPermission = (permission: string): boolean => {
    if (!user || !user.role?.permissions) return false

    const permissions = user.role.permissions as any[]

    // Check for wildcard permissions
    if (permissions.some((p: any) =>
      (typeof p === 'string' && p === '*') ||
      (typeof p === 'object' && p.module === '*' && p.actions?.includes('*'))
    )) {
      return true
    }

    // Check specific permissions
    if (typeof permissions[0] === 'string') {
      // String array format
      return permissions.includes(permission) || permissions.includes('*')
    } else {
      // Object array format
      const [module, action] = permission.split('.')
      return permissions.some((p: any) => {
        if (p.module === module || p.module === '*') {
          return p.actions?.includes(action) || p.actions?.includes('*')
        }
        return false
      })
    }
  }

  const hasRole = (roleId: string): boolean => {
    // FIXED: Map frontend role IDs to backend role names
    const roleMapping = {
      'super_admin': 'SUPERADMIN',
      'superadmin': 'SUPERADMIN',
      'admin': 'ADMIN',
      'hr_manager': 'HR_MANAGER',
      'finance_manager': 'FINANCE_MANAGER',
      'department_manager': 'DEPARTMENT_MANAGER',
      'sales_manager': 'SALES_MANAGER',
      'employee': 'EMPLOYEE',
      'user': 'USER'
    }

    const backendRoleName = roleMapping[roleId.toLowerCase() as keyof typeof roleMapping] || roleId.toUpperCase()
    return user?.role?.name === backendRoleName
  }

  const hasMinRoleLevel = (level: number): boolean => {
    return user?.role ? ('level' in user.role && (user.role as any).level <= level) : false
  }

  const canAccess = (module: string, action: string): boolean => {
    return hasPermission(`${module}.${action}`)
  }

  return {
    hasPermission,
    hasRole,
    hasMinRoleLevel,
    canAccess,
    user,
    userRole: user?.role
  }
}

// Component for conditional rendering based on permissions
interface PermissionGateProps {
  children: ReactNode
  permission?: string
  role?: string
  minRoleLevel?: number
  fallback?: ReactNode
}

export function PermissionGate({
  children,
  permission,
  role,
  minRoleLevel,
  fallback = null
}: PermissionGateProps) {
  const { hasPermission, hasRole, hasMinRoleLevel } = usePermissions()

  let hasAccess = true

  if (permission && !hasPermission(permission)) {
    hasAccess = false
  }

  if (role && !hasRole(role)) {
    hasAccess = false
  }

  if (minRoleLevel && !hasMinRoleLevel(minRoleLevel)) {
    hasAccess = false
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>
}
