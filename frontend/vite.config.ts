import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import compression from 'vite-plugin-compression'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),

    // Bundle analyzer - generates stats.html
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),

    // Gzip compression
    compression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024, // Only compress files larger than 1KB
      deleteOriginFile: false,
    }),

    // Brotli compression (better than gzip)
    compression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 1024,
      deleteOriginFile: false,
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    // Help resolve CommonJS modules properly
    dedupe: ['react', 'react-dom', 'lodash'],
  },
  optimizeDeps: {
    include: [
      // Core dependencies that should be pre-bundled
      'react',
      'react-dom',
      'react-router-dom',
      '@reduxjs/toolkit',
      'react-redux',

      // UI dependencies
      'clsx',
      'tailwind-merge',

      // Utility libraries
      'date-fns',
      'react-hook-form',

      // Keep xlsx for Excel functionality
      'xlsx',

      // Include lodash to fix module resolution issues
      'lodash',
      'lodash/get',
      'lodash/set',
      'lodash/merge',
      'lodash/debounce',
      'lodash/throttle'
    ],
    exclude: [
      // Exclude problematic dependencies
      'canvas'
    ]
  },
  server: {
    port: 5173,
    host: true,
    fs: {
      strict: false
    },
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      }
    }
  },
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    }
  },
  build: {
    // Enable source maps for production debugging
    sourcemap: false,

    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000,

    // Enable minification
    minify: 'terser',

    // Terser options for better compression
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },

    rollupOptions: {
      external: ['canvas'],

      // Manual chunk splitting for optimal loading
      output: {
        manualChunks: {
          // Vendor chunks - separate large libraries
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-redux': ['@reduxjs/toolkit', 'react-redux'],
          'vendor-ui': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-select',
            '@radix-ui/react-tabs',
            '@radix-ui/react-toast'
          ],
          'vendor-charts': ['recharts'],
          'vendor-forms': ['react-hook-form'],
          'vendor-utils': ['date-fns', 'clsx', 'tailwind-merge'],

          // Feature-based chunks
          'dashboard': [
            'src/pages/Dashboard.tsx',
            'src/pages/dashboards/AdminDashboard.tsx'
          ],
          'employees': [
            'src/pages/Employees.tsx',
            'src/pages/employee-specific/EmployeeProfile.tsx'
          ],
          'hr': [
            'src/pages/hr/LeaveManagement.tsx',
            'src/pages/hr/Attendance.tsx',
            'src/pages/hr/Performance.tsx',
            'src/pages/hr/Payroll.tsx'
          ],
          'projects': [
            'src/pages/projects/Projects.tsx',
            'src/pages/projects/Tasks.tsx'
          ],
          'sales': [
            'src/pages/sales/Sales.tsx',
            'src/pages/sales/SalesOrders.tsx',
            'src/pages/sales/Quotations.tsx',
            'src/pages/sales/SalesPipeline.tsx'
          ],
          'analytics': [
            'src/pages/Reports.tsx'
          ],
          'settings': [
            'src/pages/Settings.tsx',
            'src/pages/admin/UserManagement.tsx'
          ]
        },

        // Optimize chunk naming
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            const fileName = facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '')
            return `chunks/${fileName}-[hash].js`
          }
          return 'chunks/[name]-[hash].js'
        },

        // Optimize asset naming
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || []
          const ext = info[info.length - 1]

          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/images/[name]-[hash][extname]`
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return `assets/fonts/[name]-[hash][extname]`
          }
          return `assets/[name]-[hash][extname]`
        }
      }
    }
  }
})
