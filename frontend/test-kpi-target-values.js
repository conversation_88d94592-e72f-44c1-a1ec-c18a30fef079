/**
 * Test script to verify KPI target values are being processed correctly
 * Run this in the browser console on the KPI Management page
 */

// Test the API response directly
async function testKPITargetValues() {
  try {
    console.log('🧪 Testing KPI Target Values...')
    
    // Get the auth token
    const token = localStorage.getItem('token')
    if (!token) {
      console.error('❌ No auth token found')
      return
    }
    
    // Fetch KPI data directly from API
    const response = await fetch('http://localhost:8000/api/kpi-metrics/', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      console.error('❌ API request failed:', response.status)
      return
    }
    
    const data = await response.json()
    console.log('📊 Raw API Response:', data)
    
    // Check the first few KPIs
    const kpis = data.results || data
    if (Array.isArray(kpis)) {
      console.log(`📈 Found ${kpis.length} KPIs`)
      
      kpis.slice(0, 5).forEach((kpi, index) => {
        console.log(`🎯 KPI ${index + 1}:`, {
          id: kpi.id,
          name: kpi.name,
          name_ar: kpi.name_ar,
          target_value: kpi.target_value,
          target_type: typeof kpi.target_value,
          current_value: kpi.current_value,
          current_type: typeof kpi.current_value,
          unit: kpi.unit,
          status: kpi.status
        })
        
        // Test the transformation
        const numericTarget = typeof kpi.target_value === 'string' ? 
          parseFloat(kpi.target_value) : kpi.target_value
        
        console.log(`🔄 Transformed target for ${kpi.name}:`, {
          original: kpi.target_value,
          transformed: numericTarget,
          isValid: !isNaN(numericTarget) && numericTarget !== null
        })
      })
    } else {
      console.error('❌ Unexpected data format:', typeof kpis)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Test the formatKPIValue function
function testFormatKPIValue() {
  console.log('🧪 Testing formatKPIValue function...')
  
  // Test cases
  const testCases = [
    { value: 85, unit: '%', expected: '85.0%' },
    { value: '85.00', unit: '%', expected: '85.0%' },
    { value: 100000, unit: 'SAR', expected: 'formatted currency' },
    { value: '100000.00', unit: 'SAR', expected: 'formatted currency' },
    { value: null, unit: '%', expected: '--' },
    { value: undefined, unit: '%', expected: '--' },
    { value: 0, unit: '%', expected: '0.0%' }
  ]
  
  testCases.forEach((testCase, index) => {
    try {
      // Convert string to number if needed
      const numericValue = typeof testCase.value === 'string' ? 
        parseFloat(testCase.value) : testCase.value
      
      // This would call the actual formatKPIValue function if available
      console.log(`Test ${index + 1}:`, {
        input: testCase.value,
        inputType: typeof testCase.value,
        converted: numericValue,
        convertedType: typeof numericValue,
        unit: testCase.unit,
        isValid: !isNaN(numericValue) && numericValue !== null && numericValue !== undefined
      })
    } catch (error) {
      console.error(`Test ${index + 1} failed:`, error)
    }
  })
}

// Run the tests
console.log('🚀 Starting KPI Target Values Test...')
testKPITargetValues()
testFormatKPIValue()

console.log(`
📋 To run this test:
1. Open the KPI Management page in your browser
2. Open the browser console (F12)
3. Copy and paste this entire script
4. Check the console output for results
`)
