import { test, expect } from '@playwright/test';

/**
 * Full System Test Suite
 *
 * Comprehensive testing of:
 * 1. Backend connectivity and API endpoints
 * 2. All CRUD operations for each module
 * 3. Role-based access control
 * 4. Real-time features (WebSocket)
 * 5. Authentication flows
 * 6. Data integrity
 */

// Test users for different roles
const testUsers = {
  superadmin: { username: '<EMAIL>', password: 'admin123', role: 'SUPERADMIN' },
  hr_manager: { username: '<EMAIL>', password: 'hr123', role: 'HR_MANAGER' },
  employee: { username: '<EMAIL>', password: 'emp123', role: 'EMPLOY<PERSON>' }
};

/**
 * Full System Test Suite
 * 
 * Comprehensive testing of:
 * 1. Backend connectivity and API endpoints
 * 2. All CRUD operations for each module
 * 3. Role-based access control
 * 4. Real-time features (WebSocket)
 * 5. Authentication flows
 * 6. Data integrity
 */

test.describe('Full System Tests - Backend Integration', () => {

  // Test users for different roles
  const testUsers = {
    superadmin: { username: '<EMAIL>', password: 'admin123', role: 'SUP<PERSON>ADMIN' },
    hr_manager: { username: '<EMAIL>', password: 'hr123', role: 'HR_MANAGER' },
    employee: { username: '<EMAIL>', password: 'emp123', role: 'EMPLOYEE' }
  };
  
  test.beforeEach(async ({ page }) => {
    // Monitor for errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Store errors for later verification
    await page.addInitScript(() => {
      (window as any).testErrors = [];
    });
  });

  test('should verify backend connectivity and API endpoints', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Check if backend is responding
    const apiResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8000/api/dashboard-stats/');
        return {
          status: response.status,
          ok: response.ok,
          available: true
        };
      } catch (error) {
        return {
          status: 0,
          ok: false,
          available: false,
          error: error.message
        };
      }
    });
    
    console.log('Backend API Status:', apiResponse);
    
    // Backend should be available for full testing
    if (apiResponse.available) {
      expect(apiResponse.ok).toBe(true);
      console.log('✅ Backend is available - proceeding with full tests');
    } else {
      console.log('⚠️ Backend not available - testing frontend only');
    }
  });

  test('should test complete authentication flow', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // Test login with valid credentials
    await page.fill('#username', testUsers.superadmin.username);
    await page.fill('#password', testUsers.superadmin.password);
    
    // Monitor network requests
    const loginRequests: any[] = [];
    page.on('response', response => {
      if (response.url().includes('/api/auth/login/')) {
        loginRequests.push({
          status: response.status(),
          url: response.url(),
          ok: response.ok()
        });
      }
    });
    
    await page.click('button[type="submit"]');
    await page.waitForTimeout(5000);
    
    // Check login result
    const currentUrl = page.url();
    const hasNavigation = await page.locator('nav, [role="navigation"]').count() > 0;
    const isStillOnLogin = currentUrl.includes('/') && !currentUrl.includes('/dashboard');
    
    console.log('Login Test Results:', {
      currentUrl,
      hasNavigation,
      isStillOnLogin,
      loginRequests: loginRequests.length
    });
    
    if (loginRequests.length > 0) {
      const loginRequest = loginRequests[0];
      console.log('Login API Response:', loginRequest);
      
      if (loginRequest.ok) {
        expect(hasNavigation).toBe(true);
        console.log('✅ Authentication successful');
      } else {
        console.log('⚠️ Authentication failed - testing without login');
      }
    }
  });

  test('should test employee management CRUD operations', async ({ page }) => {
    // Try to login first
    await page.goto('/');
    await page.fill('#username', testUsers.superadmin.username);
    await page.fill('#password', testUsers.superadmin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Navigate to employees module
    await page.goto('/#/employees');
    await page.waitForTimeout(3000);
    
    // Test READ operation
    const hasEmployeeList = await page.locator('table, .employee-list, .grid-view').count() > 0;
    const hasEmployeeData = await page.locator('tr, .employee-card, .list-item').count() > 1;
    
    console.log('Employee READ Test:', { hasEmployeeList, hasEmployeeData });
    
    // Test CREATE operation
    const hasAddButton = await page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create")').count() > 0;
    
    if (hasAddButton) {
      await page.click('button:has-text("Add"), button:has-text("New"), button:has-text("Create")');
      await page.waitForTimeout(2000);
      
      const hasCreateForm = await page.locator('form, .modal, .dialog').count() > 0;
      const hasNameField = await page.locator('input[name*="name"], input[placeholder*="name"]').count() > 0;
      
      console.log('Employee CREATE Test:', { hasCreateForm, hasNameField });
      
      if (hasCreateForm && hasNameField) {
        // Try to fill and submit form
        await page.fill('input[name*="name"], input[placeholder*="name"]', 'Test Employee');
        
        const hasEmailField = await page.locator('input[type="email"], input[name*="email"]').count() > 0;
        if (hasEmailField) {
          await page.fill('input[type="email"], input[name*="email"]', '<EMAIL>');
        }
        
        const hasSubmitButton = await page.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")').count() > 0;
        if (hasSubmitButton) {
          console.log('✅ Employee CREATE form is functional');
        }
      }
    }
    
    // Test UPDATE operation (if edit buttons exist)
    const hasEditButton = await page.locator('button:has-text("Edit"), .edit-button, [data-action="edit"]').count() > 0;
    if (hasEditButton) {
      console.log('✅ Employee UPDATE functionality available');
    }
    
    // Test DELETE operation (if delete buttons exist)
    const hasDeleteButton = await page.locator('button:has-text("Delete"), .delete-button, [data-action="delete"]').count() > 0;
    if (hasDeleteButton) {
      console.log('✅ Employee DELETE functionality available');
    }
  });

  test('should test role-based access control', async ({ page }) => {
    const roleTests = [
      {
        user: testUsers.superadmin,
        allowedPaths: ['/#/employees', '/#/user-management', '/#/system-administration'],
        restrictedPaths: []
      },
      {
        user: testUsers.hr_manager,
        allowedPaths: ['/#/employees', '/#/attendance', '/#/leave-management'],
        restrictedPaths: ['/#/user-management', '/#/system-administration']
      },
      {
        user: testUsers.employee,
        allowedPaths: ['/#/dashboard', '/#/profile'],
        restrictedPaths: ['/#/user-management', '/#/employees', '/#/system-administration']
      }
    ];
    
    for (const roleTest of roleTests) {
      console.log(`Testing role: ${roleTest.user.role}`);
      
      // Login as this role
      await page.goto('/');
      await page.fill('#username', roleTest.user.username);
      await page.fill('#password', roleTest.user.password);
      await page.click('button[type="submit"]');
      await page.waitForTimeout(3000);
      
      // Test allowed paths
      for (const allowedPath of roleTest.allowedPaths) {
        await page.goto(allowedPath);
        await page.waitForTimeout(2000);
        
        const hasErrorBoundary = await page.locator('.error-boundary').count();
        const hasUnauthorized = await page.locator('text=Unauthorized, text=Access Denied').count();
        const isRedirectedToLogin = page.url().includes('/') && !page.url().includes(allowedPath.replace('/#', ''));
        
        const hasAccess = hasErrorBoundary === 0 && hasUnauthorized === 0 && !isRedirectedToLogin;
        console.log(`${roleTest.user.role} access to ${allowedPath}: ${hasAccess ? '✅ ALLOWED' : '❌ DENIED'}`);
      }
      
      // Test restricted paths
      for (const restrictedPath of roleTest.restrictedPaths) {
        await page.goto(restrictedPath);
        await page.waitForTimeout(2000);
        
        const hasUnauthorized = await page.locator('text=Unauthorized, text=Access Denied').count() > 0;
        const isRedirectedToLogin = page.url().includes('/') && !page.url().includes(restrictedPath.replace('/#', ''));
        const isBlocked = hasUnauthorized || isRedirectedToLogin;
        
        console.log(`${roleTest.user.role} access to ${restrictedPath}: ${isBlocked ? '✅ BLOCKED' : '❌ ALLOWED'}`);
      }
      
      // Logout for next test
      await page.goto('/');
    }
  });

  test('should test KPI dashboard and real-time features', async ({ page }) => {
    // Login first
    await page.goto('/');
    await page.fill('#username', testUsers.superadmin.username);
    await page.fill('#password', testUsers.superadmin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Navigate to KPI dashboard
    await page.goto('/#/kpi/dashboard');
    await page.waitForTimeout(3000);
    
    // Test KPI dashboard loading
    const hasKPICards = await page.locator('.kpi-card, [class*="kpi"], [data-testid*="kpi"]').count();
    const hasCharts = await page.locator('canvas, .chart, svg').count();
    const hasRealTimeIndicators = await page.locator('.live, .real-time, .updating').count();
    
    console.log('KPI Dashboard Test:', {
      kpiCards: hasKPICards,
      charts: hasCharts,
      realTimeIndicators: hasRealTimeIndicators
    });
    
    // Test WebSocket connection (if available)
    const webSocketTest = await page.evaluate(() => {
      return new Promise((resolve) => {
        try {
          const ws = new WebSocket('ws://localhost:8000/ws/kpi/');
          
          ws.onopen = () => {
            resolve({ connected: true, error: null });
            ws.close();
          };
          
          ws.onerror = (error) => {
            resolve({ connected: false, error: 'Connection failed' });
          };
          
          // Timeout after 5 seconds
          setTimeout(() => {
            resolve({ connected: false, error: 'Timeout' });
            ws.close();
          }, 5000);
        } catch (error) {
          resolve({ connected: false, error: error.message });
        }
      });
    });
    
    console.log('WebSocket Test:', webSocketTest);
    
    // Test real-time updates (if refresh button exists)
    const hasRefreshButton = await page.locator('button:has-text("Refresh"), button:has-text("Update")').count() > 0;
    if (hasRefreshButton) {
      await page.click('button:has-text("Refresh"), button:has-text("Update")');
      await page.waitForTimeout(2000);
      
      const hasErrorAfterRefresh = await page.locator('.error-boundary').count();
      expect(hasErrorAfterRefresh).toBe(0);
      console.log('✅ Real-time refresh functionality works');
    }
  });

  test('should test all major modules for basic functionality', async ({ page }) => {
    // Login first
    await page.goto('/');
    await page.fill('#username', testUsers.superadmin.username);
    await page.fill('#password', testUsers.superadmin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    const modules = [
      { path: '/#/dashboard', name: 'Dashboard' },
      { path: '/#/employees', name: 'Employees' },
      { path: '/#/departments', name: 'Departments' },
      { path: '/#/attendance', name: 'Attendance' },
      { path: '/#/leave-management', name: 'Leave Management' },
      { path: '/#/projects', name: 'Projects' },
      { path: '/#/tasks', name: 'Tasks' },
      { path: '/#/finance', name: 'Finance' },
      { path: '/#/reports', name: 'Reports' },
      { path: '/#/analytics', name: 'Analytics' },
      { path: '/#/settings', name: 'Settings' }
    ];
    
    const moduleResults = {};
    
    for (const module of modules) {
      try {
        await page.goto(module.path);
        await page.waitForTimeout(2000);
        
        // Check if module loads without errors
        const hasErrorBoundary = await page.locator('.error-boundary').count();
        const hasContent = await page.locator('main, .main-content, [role="main"], .page-content').count() > 0;
        const hasTitle = await page.locator('h1, h2, .page-title, .module-title').count() > 0;
        const hasNavigation = await page.locator('nav, .navigation, .breadcrumb').count() > 0;
        
        moduleResults[module.name] = {
          loaded: hasErrorBoundary === 0,
          hasContent,
          hasTitle,
          hasNavigation,
          error: null
        };
        
        console.log(`${module.name}: ${hasErrorBoundary === 0 ? '✅ LOADED' : '❌ ERROR'}`);
        
      } catch (error) {
        moduleResults[module.name] = {
          loaded: false,
          hasContent: false,
          hasTitle: false,
          hasNavigation: false,
          error: error.message
        };
        
        console.log(`${module.name}: ❌ FAILED - ${error.message}`);
      }
    }
    
    // Summary
    const totalModules = modules.length;
    const loadedModules = Object.values(moduleResults).filter((result: any) => result.loaded).length;
    const successRate = Math.round((loadedModules / totalModules) * 100);
    
    console.log(`\n📊 Module Test Summary: ${loadedModules}/${totalModules} modules loaded (${successRate}%)`);
    
    // At least 80% of modules should load successfully
    expect(successRate).toBeGreaterThanOrEqual(80);
  });

  test('should verify data integrity and API responses', async ({ page }) => {
    // Test API endpoints directly
    const apiEndpoints = [
      '/api/dashboard-stats/',
      '/api/employees/',
      '/api/departments/',
      '/api/projects/',
      '/api/notifications/notifications/'
    ];
    
    const apiResults = {};
    
    for (const endpoint of apiEndpoints) {
      const result = await page.evaluate(async (url) => {
        try {
          const response = await fetch(`http://localhost:8000${url}`);
          const data = await response.json();
          
          return {
            status: response.status,
            ok: response.ok,
            hasData: data && (Array.isArray(data) ? data.length > 0 : Object.keys(data).length > 0),
            dataType: Array.isArray(data) ? 'array' : typeof data
          };
        } catch (error) {
          return {
            status: 0,
            ok: false,
            hasData: false,
            error: error.message
          };
        }
      }, endpoint);
      
      apiResults[endpoint] = result;
      console.log(`API ${endpoint}: ${result.ok ? '✅ OK' : '❌ FAILED'} (${result.status})`);
    }
    
    // At least some APIs should be working
    const workingAPIs = Object.values(apiResults).filter((result: any) => result.ok).length;
    expect(workingAPIs).toBeGreaterThanOrEqual(1);
  });

});
