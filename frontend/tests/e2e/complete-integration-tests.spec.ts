import { test, expect } from '@playwright/test';

/**
 * Complete Integration Test Suite
 * 
 * Tests:
 * 1. Complete authentication flow
 * 2. All CRUD operations with backend
 * 3. Role-based access control
 * 4. Real-time features
 * 5. Data integrity
 */

test.describe('Complete Integration Tests', () => {
  
  // Test users with different roles
  const testUsers = {
    admin: { username: 'admin', password: 'admin123', role: 'ADMIN' },
    hr: { username: 'hr_manager', password: 'hr123', role: 'HR_MANAGER' },
    employee: { username: 'employee', password: 'emp123', role: 'EMPLOYEE' }
  };

  test.beforeEach(async ({ page }) => {
    // Set longer timeout for backend operations
    test.setTimeout(60000);
    
    // Monitor console errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Store errors for verification
    await page.addInitScript(() => {
      (window as any).testErrors = [];
    });
  });

  test('should complete full authentication flow with backend', async ({ page }) => {
    console.log('🔐 Testing Complete Authentication Flow...');
    
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Test login with admin credentials
    await page.fill('input[name="username"], input[placeholder*="اسم"], #username', testUsers.admin.username);
    await page.fill('input[name="password"], input[type="password"], #password', testUsers.admin.password);
    
    // Monitor network requests
    const loginRequests: any[] = [];
    page.on('response', response => {
      if (response.url().includes('/api/auth/login/')) {
        loginRequests.push({
          status: response.status(),
          url: response.url(),
          ok: response.ok()
        });
      }
    });
    
    // Submit login form
    await page.click('button[type="submit"], button:has-text("تسجيل"), button:has-text("Login")');
    
    // Wait for authentication to complete (up to 30 seconds)
    await page.waitForTimeout(30000);
    
    // Check authentication result
    const currentUrl = page.url();
    const hasNavigation = await page.locator('nav, [role="navigation"], .sidebar, .header').count() > 0;
    const hasUserInfo = await page.locator('.user-info, .profile, .avatar').count() > 0;
    const isAuthenticated = !currentUrl.includes('/login') && !currentUrl.endsWith('/');
    
    console.log('Authentication Results:', {
      currentUrl,
      hasNavigation,
      hasUserInfo,
      isAuthenticated,
      loginRequests: loginRequests.length
    });
    
    if (loginRequests.length > 0) {
      const loginRequest = loginRequests[0];
      console.log('Login API Response:', loginRequest);
      
      if (loginRequest.ok) {
        // Successful authentication
        expect(hasNavigation || isAuthenticated).toBe(true);
        console.log('✅ Authentication successful');
      } else {
        console.log('⚠️ Authentication failed - testing error handling');
        // Should show error message
        const hasErrorMessage = await page.locator('.error, .alert, [role="alert"]').count() > 0;
        console.log('Error handling:', { hasErrorMessage });
      }
    } else {
      console.log('⚠️ No login requests detected - may be timeout issue');
    }
  });

  test('should test employee CRUD operations with backend', async ({ page }) => {
    console.log('👥 Testing Employee CRUD Operations...');
    
    // Login first
    await page.goto('/');
    await page.fill('input[name="username"], #username', testUsers.admin.username);
    await page.fill('input[name="password"], #password', testUsers.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(10000);
    
    // Navigate to employees module
    await page.goto('/#/employees');
    await page.waitForTimeout(5000);
    
    // Test READ operation
    const hasEmployeeList = await page.locator('table, .employee-list, .data-table').count() > 0;
    const hasEmployeeRows = await page.locator('tr, .employee-row, .list-item').count() > 1;
    const hasLoadingIndicator = await page.locator('.loading, .spinner').count() > 0;
    
    console.log('Employee READ Test:', { 
      hasEmployeeList, 
      hasEmployeeRows, 
      hasLoadingIndicator,
      url: page.url()
    });
    
    // Test CREATE operation
    const hasAddButton = await page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("إضافة"), .add-btn').count() > 0;
    
    if (hasAddButton) {
      console.log('Testing CREATE operation...');
      await page.click('button:has-text("Add"), button:has-text("New"), button:has-text("إضافة"), .add-btn');
      await page.waitForTimeout(3000);
      
      const hasCreateForm = await page.locator('form, .modal, .dialog, .form-container').count() > 0;
      const hasNameField = await page.locator('input[name*="name"], input[placeholder*="name"], input[placeholder*="اسم"]').count() > 0;
      const hasEmailField = await page.locator('input[type="email"], input[name*="email"]').count() > 0;
      
      console.log('Employee CREATE Test:', { hasCreateForm, hasNameField, hasEmailField });
      
      if (hasCreateForm && hasNameField) {
        // Fill the form
        await page.fill('input[name*="name"], input[placeholder*="name"]', 'Test Employee Integration');
        
        if (hasEmailField) {
          await page.fill('input[type="email"], input[name*="email"]', '<EMAIL>');
        }
        
        // Try to save
        const hasSaveButton = await page.locator('button:has-text("Save"), button:has-text("Create"), button:has-text("حفظ")').count() > 0;
        
        if (hasSaveButton) {
          console.log('Submitting CREATE form...');
          await page.click('button:has-text("Save"), button:has-text("Create"), button:has-text("حفظ")');
          await page.waitForTimeout(5000);
          
          // Check for success/error feedback
          const hasSuccessMessage = await page.locator('.success, .alert-success, .notification-success').count() > 0;
          const hasErrorMessage = await page.locator('.error, .alert-error, .notification-error').count() > 0;
          
          console.log('CREATE Result:', { hasSuccessMessage, hasErrorMessage });
        }
      }
    }
    
    // Test UPDATE operation
    if (hasEmployeeRows) {
      console.log('Testing UPDATE operation...');
      const hasEditButton = await page.locator('button:has-text("Edit"), .edit-btn, [data-action="edit"]').count() > 0;
      
      if (hasEditButton) {
        await page.click('button:has-text("Edit"), .edit-btn, [data-action="edit"]');
        await page.waitForTimeout(3000);
        
        const hasEditForm = await page.locator('form, .modal, .dialog').count() > 0;
        console.log('Employee UPDATE Test:', { hasEditButton, hasEditForm });
      }
    }
    
    // Test DELETE operation
    if (hasEmployeeRows) {
      console.log('Testing DELETE operation...');
      const hasDeleteButton = await page.locator('button:has-text("Delete"), .delete-btn, [data-action="delete"]').count() > 0;
      console.log('Employee DELETE Test:', { hasDeleteButton });
    }
  });

  test('should test department CRUD operations', async ({ page }) => {
    console.log('🏢 Testing Department CRUD Operations...');
    
    // Login and navigate to departments
    await page.goto('/');
    await page.fill('input[name="username"], #username', testUsers.admin.username);
    await page.fill('input[name="password"], #password', testUsers.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(10000);
    
    await page.goto('/#/departments');
    await page.waitForTimeout(5000);
    
    // Test department operations
    const hasDepartmentList = await page.locator('table, .department-list, .data-table').count() > 0;
    const hasDepartmentData = await page.locator('tr, .department-row, .list-item').count() > 1;
    
    console.log('Department READ Test:', { hasDepartmentList, hasDepartmentData });
    
    // Test CREATE department
    const hasAddButton = await page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("إضافة")').count() > 0;
    
    if (hasAddButton) {
      console.log('Testing Department CREATE...');
      await page.click('button:has-text("Add"), button:has-text("New"), button:has-text("إضافة")');
      await page.waitForTimeout(3000);
      
      const hasCreateForm = await page.locator('form, .modal, .dialog').count() > 0;
      console.log('Department CREATE Test:', { hasCreateForm });
      
      if (hasCreateForm) {
        // Fill department form
        const hasNameField = await page.locator('input[name*="name"], input[placeholder*="name"]').count() > 0;
        
        if (hasNameField) {
          await page.fill('input[name*="name"], input[placeholder*="name"]', 'Test Integration Department');
          
          const hasSaveButton = await page.locator('button:has-text("Save"), button:has-text("Create")').count() > 0;
          
          if (hasSaveButton) {
            await page.click('button:has-text("Save"), button:has-text("Create")');
            await page.waitForTimeout(5000);
            
            const hasSuccessMessage = await page.locator('.success, .alert-success').count() > 0;
            console.log('Department CREATE Result:', { hasSuccessMessage });
          }
        }
      }
    }
  });

  test('should test role-based access control with backend', async ({ page }) => {
    console.log('🔒 Testing Role-Based Access Control...');
    
    const roleTests = [
      {
        user: testUsers.admin,
        allowedPaths: ['/#/employees', '/#/departments', '/#/user-management'],
        restrictedPaths: []
      },
      {
        user: testUsers.hr,
        allowedPaths: ['/#/employees', '/#/attendance'],
        restrictedPaths: ['/#/user-management', '/#/system-administration']
      }
    ];
    
    for (const roleTest of roleTests) {
      console.log(`Testing role: ${roleTest.user.role}`);
      
      // Login as this role
      await page.goto('/');
      await page.fill('input[name="username"], #username', roleTest.user.username);
      await page.fill('input[name="password"], #password', roleTest.user.password);
      await page.click('button[type="submit"]');
      await page.waitForTimeout(15000);
      
      // Test allowed paths
      for (const allowedPath of roleTest.allowedPaths) {
        await page.goto(allowedPath);
        await page.waitForTimeout(3000);
        
        const hasContent = await page.locator('main, .main-content, .page-content').count() > 0;
        const hasErrorBoundary = await page.locator('.error-boundary').count() === 0;
        const hasUnauthorized = await page.locator('text=Unauthorized, text=Access Denied').count() === 0;
        
        const isAccessible = hasContent && hasErrorBoundary && hasUnauthorized;
        
        console.log(`${roleTest.user.role} accessing ${allowedPath}:`, {
          hasContent,
          hasErrorBoundary,
          hasUnauthorized,
          isAccessible
        });
      }
      
      // Test restricted paths
      for (const restrictedPath of roleTest.restrictedPaths) {
        await page.goto(restrictedPath);
        await page.waitForTimeout(3000);
        
        const hasUnauthorized = await page.locator('text=Unauthorized, text=Access Denied, text=403').count() > 0;
        const isRedirectedToLogin = page.url().includes('/login') || page.url().endsWith('/');
        const isRedirectedToDashboard = page.url().includes('/dashboard');
        
        const isProperlyRestricted = hasUnauthorized || isRedirectedToLogin || isRedirectedToDashboard;
        
        console.log(`${roleTest.user.role} restricted from ${restrictedPath}:`, {
          hasUnauthorized,
          isRedirectedToLogin,
          isRedirectedToDashboard,
          isProperlyRestricted
        });
        
        // Should be properly restricted
        expect(isProperlyRestricted).toBe(true);
      }
    }
  });

  test('should test KPI dashboard with real-time features', async ({ page }) => {
    console.log('📊 Testing KPI Dashboard with Real-time Features...');
    
    // Login and navigate to KPI dashboard
    await page.goto('/');
    await page.fill('input[name="username"], #username', testUsers.admin.username);
    await page.fill('input[name="password"], #password', testUsers.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(10000);
    
    await page.goto('/#/kpi/dashboard');
    await page.waitForTimeout(5000);
    
    // Test KPI dashboard components
    const hasKpiCards = await page.locator('.kpi-card, .metric-card, .stat-card').count();
    const hasCharts = await page.locator('canvas, .chart, svg').count();
    const hasRealTimeIndicators = await page.locator('.live, .real-time, .updating').count();
    
    console.log('KPI Dashboard Test:', { hasKpiCards, hasCharts, hasRealTimeIndicators });
    
    // Test real-time updates
    const hasRefreshButton = await page.locator('button:has-text("Refresh"), button:has-text("Update")').count() > 0;
    
    if (hasRefreshButton) {
      console.log('Testing real-time refresh...');
      await page.click('button:has-text("Refresh"), button:has-text("Update")');
      await page.waitForTimeout(5000);
      
      // Should not cause errors
      const hasErrorAfterRefresh = await page.locator('.error-boundary').count();
      expect(hasErrorAfterRefresh).toBe(0);
      
      console.log('Real-time refresh completed without errors');
    }
    
    // Test WebSocket connection
    const webSocketStatus = await page.evaluate(() => {
      return new Promise((resolve) => {
        if ('WebSocket' in window) {
          try {
            const ws = new WebSocket('ws://localhost:8000/ws/kpi/');
            
            ws.onopen = () => {
              ws.close();
              resolve({ connected: true, error: null });
            };
            
            ws.onerror = () => {
              resolve({ connected: false, error: 'Connection failed' });
            };
            
            setTimeout(() => {
              ws.close();
              resolve({ connected: false, error: 'Timeout' });
            }, 5000);
            
          } catch (error) {
            resolve({ connected: false, error: error.message });
          }
        } else {
          resolve({ connected: false, error: 'WebSocket not supported' });
        }
      });
    });
    
    console.log('WebSocket Test Results:', webSocketStatus);
  });

  test('should verify data integrity across modules', async ({ page }) => {
    console.log('🔍 Testing Data Integrity Across Modules...');
    
    // Login
    await page.goto('/');
    await page.fill('input[name="username"], #username', testUsers.admin.username);
    await page.fill('input[name="password"], #password', testUsers.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(10000);
    
    // Test data consistency across different modules
    const modules = [
      { path: '/#/dashboard', name: 'Dashboard' },
      { path: '/#/employees', name: 'Employees' },
      { path: '/#/departments', name: 'Departments' },
      { path: '/#/kpi/dashboard', name: 'KPI Dashboard' }
    ];
    
    const moduleResults = {};
    
    for (const module of modules) {
      console.log(`Testing data integrity in: ${module.name}`);
      
      await page.goto(module.path);
      await page.waitForTimeout(5000);
      
      // Check if module loads without errors
      const hasErrorBoundary = await page.locator('.error-boundary').count() === 0;
      const hasContent = await page.locator('main, .main-content, [role="main"]').count() > 0;
      const hasData = await page.locator('table, .data-table, .chart, canvas').count() > 0;
      
      moduleResults[module.name] = {
        loaded: hasErrorBoundary,
        hasContent,
        hasData,
        url: page.url()
      };
      
      console.log(`${module.name} Results:`, moduleResults[module.name]);
    }
    
    // Verify that most modules loaded successfully
    const loadedModules = Object.values(moduleResults).filter((result: any) => result.loaded).length;
    const totalModules = modules.length;
    const successRate = (loadedModules / totalModules) * 100;
    
    console.log(`Data Integrity Test: ${successRate}% (${loadedModules}/${totalModules})`);
    
    // At least 75% of modules should load successfully
    expect(successRate).toBeGreaterThanOrEqual(75);
  });

});
