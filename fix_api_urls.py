#!/usr/bin/env python3
"""
Fix API URLs Script
Systematically fixes all hardcoded API URLs in the frontend to use port 8000
"""

import os
import re
from pathlib import Path

def fix_api_urls_in_file(file_path: Path) -> bool:
    """Fix API URLs in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix localhost:8001 -> localhost:8000
        content = re.sub(r'localhost:8001', 'localhost:8000', content)
        
        # Fix localhost:8002 -> localhost:8000  
        content = re.sub(r'localhost:8002', 'localhost:8000', content)
        
        # Fix any other common wrong ports
        content = re.sub(r'localhost:8003', 'localhost:8000', content)
        content = re.sub(r'localhost:3001', 'localhost:8000', content)
        
        # Fix WebSocket URLs
        content = re.sub(r'ws://localhost:8001', 'ws://localhost:8000', content)
        content = re.sub(r'ws://localhost:8002', 'ws://localhost:8000', content)
        
        # Fix environment variable defaults
        content = re.sub(
            r"import\.meta\.env\.VITE_API_BASE_URL \|\| 'http://localhost:800[1-9]/api'",
            "import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'",
            content
        )
        
        content = re.sub(
            r"import\.meta\.env\.VITE_WS_URL \|\| 'ws://localhost:800[1-9]/ws'",
            "import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws'",
            content
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all API URLs"""
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return
    
    # File patterns to search
    patterns = [
        "**/*.ts",
        "**/*.tsx", 
        "**/*.js",
        "**/*.jsx",
        "**/*.json",
        "**/*.py",
        "vite.config.ts",
        ".env*"
    ]
    
    files_to_process = set()
    
    # Collect all files
    for pattern in patterns:
        files_to_process.update(frontend_dir.glob(pattern))
    
    # Also check specific files
    specific_files = [
        "public/sw.js",
        "public/sw-fix.js", 
        "tests/e2e/full-system-tests.spec.ts"
    ]
    
    for file_path in specific_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            files_to_process.add(full_path)
    
    print(f"🔍 Found {len(files_to_process)} files to check")
    
    fixed_count = 0
    
    for file_path in sorted(files_to_process):
        if file_path.is_file():
            if fix_api_urls_in_file(file_path):
                print(f"✅ Fixed: {file_path}")
                fixed_count += 1
    
    print(f"\n🎉 Fixed API URLs in {fixed_count} files")
    
    # Also fix the Python test file if it exists
    test_file = Path("fix_syntax_errors.py")
    if test_file.exists():
        if fix_api_urls_in_file(test_file):
            print(f"✅ Fixed: {test_file}")
            fixed_count += 1

if __name__ == "__main__":
    main()
